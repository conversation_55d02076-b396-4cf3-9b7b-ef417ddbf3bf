#!/usr/bin/env python3
"""
All-Python bootstrap for EKS-Anywhere (Docker) + MetalLB + ingress-nginx + TLS + Storage.

- No shell scripts. Host prep (APT, optional LVM grow, NFS export) is done here.
- One external template file: eksa-all-in-one.yaml (kept outside of Python).
- Auto-detects Docker network CIDRs and adds them to /etc/exports.d/eks-<cluster>.exports.
- If MetalLB IP not reachable on Docker provider, auto-fallback ingress to NodePort.

Requires: kubectl, helm, eksctl (+ eksctl-anywhere), docker, python3 (jinja2, pyyaml, requests, rich optional)
Files in same dir:
  - cluster_setup.py
  - eksa-all-in-one.yaml
"""

import os, sys, time, socket, shutil, subprocess, ipaddress, shlex, json
import yaml, requests
from jinja2 import Environment, FileSystemLoader

# Optional pretty output
try:
    from rich.console import Console
    from rich.table import Table
    from rich.panel import Panel
    from rich.text import Text
    RICH = True
    CON = Console()
except Exception:
    RICH = False
    CON = None

PHASE = {}
T0 = time.perf_counter()
TEMPLATE_FILE = "eksa-all-in-one.yaml"

# ---------- pretty helpers ----------
def banner(title: str):
    if RICH: CON.rule(Text(title, style="bold cyan"))
    else:
        line = "─" * max(60, len(title) + 4)
        print(f"\n{line}\n{title}\n{line}")

def start(name: str):
    print(f"\n>> {name} ...")
    PHASE[name] = [time.perf_counter(), None, None]

def done(name: str, ok: bool = True):
    if name not in PHASE: return
    dt = time.perf_counter() - PHASE[name][0]
    PHASE[name][1] = dt
    PHASE[name][2] = ok
    if RICH:
        CON.print(f"<< {name} {'[green]OK[/green]' if ok else '[yellow]WARN[/yellow]'} ([cyan]{dt:.1f}s[/cyan])")
    else:
        print(f"<< {name} {'OK' if ok else 'WARN'} ({dt:.1f}s)")

def summary():
    total = time.perf_counter() - T0
    if RICH and PHASE:
        table = Table(title="Run Summary", header_style="bold magenta")
        table.add_column("Phase", style="cyan", no_wrap=True)
        table.add_column("Status", style="bold")
        table.add_column("Seconds", justify="right")
        for name, (_, dt, ok) in PHASE.items():
            table.add_row(name, "[green]OK" if ok else "[yellow]WARN", f"{dt:.1f}")
        table.add_section(); table.add_row("[bold]TOTAL", "", f"{total:.1f}")
        CON.print(table)
    else:
        print("\nRun Summary")
        for n, (_, dt, ok) in PHASE.items():
            print(f" - {n}: {'OK' if ok else 'WARN'} ({dt:.1f}s)")
        print(f"TOTAL: {total:.1f}s\n")

# ---------- shell helpers ----------
def which(b): return shutil.which(b)
def is_root(): return os.geteuid() == 0

def run(cmd, check=True, capture=False, text=True):
    if RICH:
        from rich.text import Text as _Text
        CON.print(_Text("[cmd] ", style="dim") + _Text(shlex.join(cmd), style="bold"))
    else:
        print(f"[cmd] {' '.join(cmd)}")
    return subprocess.run(cmd, check=check, capture_output=capture, text=text)

def sudoify(cmd):
    if is_root(): return cmd
    return ["sudo"] + cmd

def prompt(msg, default):
    v = input(f"{msg} [{default}]: ").strip()
    return v or default

def kget_jsonpath(ns, kind, name, jp):
    base = ["kubectl"]
    if ns: base += ["-n", ns]
    base += ["get", kind, name, "-o", f"jsonpath={jp}"]
    return subprocess.check_output(base, text=True).strip()

def tcp_open(host, port, to=2.0):
    try:
        with socket.create_connection((host,port), timeout=to): return True
    except OSError: return False

def ping_replies(ip: str, timeout_s: int = 1) -> bool:
    if not which("ping"):
        try:
            run(sudoify(["apt-get","update","-y"]), check=False)
            run(sudoify(["apt-get","install","-y","iputils-ping"]), check=False)
        except Exception:
            return False
    res = subprocess.run(["ping","-c","1","-W",str(timeout_s), ip],
                         stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
    return res.returncode == 0

def ip_range_from_pool(pool: str):
    start_s, end_s = pool.split("-")
    a = ipaddress.IPv4Address(start_s.strip())
    b = ipaddress.IPv4Address(end_s.strip())
    if b < a: a, b = b, a
    cur = a
    while cur <= b:
        yield str(cur)
        cur += 1

def choose_available_ip(preferred_ip: str, pool: str) -> str | None:
    print(f"[info] Checking availability of {preferred_ip} ...")
    if not ping_replies(preferred_ip):
        print(f"[✓] {preferred_ip} appears free (no ICMP reply)")
        return preferred_ip
    print(f"[warn] {preferred_ip} replied to ping → scanning pool {pool} ...")
    for ip in ip_range_from_pool(pool):
        if ip == preferred_ip: continue
        if not ping_replies(ip):
            print(f"[✓] Selected free IP: {ip}")
            return ip
    return None

def helmv(ns, release) -> bool:
    return subprocess.run(["helm","status",release,"-n",ns], capture_output=True, text=True).returncode == 0

def kexists(ns, kind, name) -> bool:
    base = ["kubectl"]
    if ns: base += ["-n",ns]
    base += ["get",kind,name]
    return subprocess.run(base, capture_output=True, text=True).returncode == 0

def kube_reachable():
    return subprocess.run(["kubectl","cluster-info"], capture_output=True, text=True).returncode == 0

def detect_or_set_kubeconfig(name: str) -> str | None:
    cur = os.environ.get("KUBECONFIG")
    if cur and os.path.exists(cur): return cur
    path = os.path.abspath(f"./{name}/{name}-eks-a-cluster.kubeconfig")
    if os.path.exists(path):
        os.environ["KUBECONFIG"] = path
        print(f"[info] Using KUBECONFIG={path}")
        return path
    return None

def is_docker_provider(cluster_name: str) -> bool:
    try:
        kind = subprocess.check_output(
            ["kubectl","get","cluster",cluster_name,"-o","jsonpath={.spec.datacenterRef.kind}"],
            text=True
        ).strip().lower()
        return kind.startswith("docker")
    except Exception:
        return subprocess.run(["kubectl","get","ns","capd-system"], capture_output=True).returncode == 0

# ---------- docker network discovery ----------
def discover_docker_subnets_for_cluster(cluster_name: str):
    """Return list of Docker network CIDRs used by node containers of this cluster."""
    cidrs = set()
    try:
        lines = subprocess.check_output(
            ["docker","ps","--format","{{.ID}} {{.Names}}"],
            text=True
        ).splitlines()
        net_names = set()
        for line in lines:
            parts = line.strip().split(maxsplit=1)
            if len(parts) != 2: continue
            cid, name = parts
            # EKS-A Docker node containers include the cluster name in the container name
            if cluster_name not in name: continue
            info = json.loads(subprocess.check_output(["docker","inspect",cid], text=True))[0]
            nets = info.get("NetworkSettings",{}).get("Networks",{}) or {}
            for net in nets.keys():
                net_names.add(net)
        for net in net_names:
            arr = json.loads(subprocess.check_output(["docker","network","inspect",net], text=True))
            if not arr: continue
            for cfg in (arr[0].get("IPAM",{}) or {}).get("Config",[]) or []:
                subnet = cfg.get("Subnet")
                if subnet: cidrs.add(subnet)
    except Exception as e:
        print(f"[host] Could not auto-detect docker subnets: {e}")
    return sorted(cidrs)

# ---------- template handling ----------
def render_all_in_one(ctx: dict) -> str:
    env = Environment(loader=FileSystemLoader("."), autoescape=False, trim_blocks=True, lstrip_blocks=True)
    if not os.path.exists(TEMPLATE_FILE):
        print(f"❌ Missing template file: {TEMPLATE_FILE}")
        sys.exit(1)
    return env.get_template(TEMPLATE_FILE).render(**ctx)

def split_docs(yaml_text: str):
    eksa, extras = [], []
    for doc in yaml.safe_load_all(yaml_text):
        if not isinstance(doc, dict): continue
        api = str(doc.get("apiVersion",""))
        kind = doc.get("kind","")
        if api.startswith("anywhere.eks.amazonaws.com/") and kind in ("Cluster","DockerDatacenterConfig"):
            eksa.append(doc)
        else:
            extras.append(doc)
    return eksa, extras

def dump_yaml(docs) -> str:
    return "\n---\n".join(yaml.safe_dump(d, sort_keys=False) for d in docs) + ("\n" if docs else "")

# ---------- preflight ----------
def ensure_tools():
    start("Check tools")
    missing = [x for x in ("kubectl","helm","eksctl") if not which(x)]
    if missing:
        print(f"❌ Missing tools: {', '.join(missing)}")
        done("Check tools", False); sys.exit(1)
    done("Check tools", True)

def ensure_eksctl_anywhere():
    start("Check eksctl-anywhere")
    try:
        subprocess.run(["eksctl","anywhere","version"], check=True, capture_output=True, text=True)
        done("Check eksctl-anywhere", True)
    except Exception:
        print("❌ eksctl-anywhere plugin missing. Install and re-run.")
        done("Check eksctl-anywhere", False); sys.exit(1)

def ensure_docker_running():
    start("Check Docker")
    if subprocess.run(["docker","info"], capture_output=True, text=True).returncode != 0:
        print("❌ Docker is not running. Try: sudo systemctl enable --now docker")
        done("Check Docker", False); sys.exit(1)
    done("Check Docker", True)

# ---------- cluster create ----------
def create_eksa_docker_cluster(cluster_name, ctx_for_template: dict):
    start("EKS-A create (Docker)")
    rendered = render_all_in_one(ctx_for_template)
    eksa_docs, _ = split_docs(rendered)
    if not eksa_docs:
        print("❌ No EKS-A docs in template.")
        done("EKS-A create (Docker)", False); sys.exit(1)
    cfg_path = os.path.abspath(f"{cluster_name}.eksa.yaml")
    with open(cfg_path, "w") as f: f.write(dump_yaml(exsa := eksa_docs))
    print(f"[info] Wrote EKS-A config: {cfg_path}")

    kc = detect_or_set_kubeconfig(cluster_name)
    if kc and kube_reachable():
        print("[info] Found existing kubeconfig; reusing this cluster.")
        done("EKS-A create (Docker)", True); return

    try:
        run(["eksctl","anywhere","create","cluster","-f",cfg_path], check=True, capture=False)
    except subprocess.CalledProcessError as e:
        blob = (e.stderr or "") + (e.stdout or "")
        if "old cluster config file exists under" in blob:
            print("[warn] Cluster folder already exists; trying to reuse.")
        else:
            done("EKS-A create (Docker)", False); sys.exit(1)

    kc = detect_or_set_kubeconfig(cluster_name)
    if not kc:
        print("❌ Expected kubeconfig not found after create.")
        done("EKS-A create (Docker)", False); sys.exit(1)

    run(["kubectl","get","nodes"])
    done("EKS-A create (Docker)", True)

# ---------- config ----------
class Config:
    def __init__(self, name, domain_suffix, lb_pool, lb_ip):
        self.name = name
        self.domain_suffix = domain_suffix
        self.lb_ip_pool = lb_pool
        self.lb_static_ip = lb_ip
        self.env_prefix = name
        self.root_zone = f"{self.env_prefix}.{self.domain_suffix}"
        self.wildcard_secret = f"wildcard-{self.name}-tls"
        self.rel_cert_mgr = f"cert-manager-{self.name}"
        self.rel_metallb  = f"metallb-{self.name}"
        self.rel_ingress  = f"ingress-nginx-{self.name}"
        self.cm_deploy  = self.rel_cert_mgr
        self.cm_webhook = f"{self.rel_cert_mgr}-webhook"
        self.cm_cainj   = f"{self.rel_cert_mgr}-cainjector"
        self.ml_ctrl    = f"{self.rel_metallb}-controller"
        self.ml_speaker = f"{self.rel_metallb}-speaker"
        self.ingress_svc = f"{self.rel_ingress}-controller"
        self.namespaces = {"metallb":"metallb-system","certmgr":"cert-manager","ingress":"ingress-nginx"}
        self._validate()

    def _validate(self):
        if "-" not in self.lb_ip_pool or ":" in self.lb_ip_pool:
            raise ValueError("LB pool must be like *************-*************")
        if self.lb_static_ip.count(".") != 3:
            raise ValueError("LB IP must be IPv4")

    @property
    def render_ctx(self):
        return {
            "CLUSTER_NAME": self.name,
            "KUBERNETES_VERSION": "1.31",
            "POD_CIDR": "**********/16",
            "SERVICE_CIDR": "*********/12",
            "CONTROL_PLANE_COUNT": 1,
            "WORKER_COUNT": 1,
            "EXTERNAL_ETCD_COUNT": 1,
            "DOMAIN": self.domain_suffix,
            "LB_POOL": self.lb_ip_pool,
            "LB_IP": self.lb_static_ip,
        }

# ---------- apply extras (with ordering) ----------
class ExtrasFromTemplate:
    ORDER = ["Namespace","Issuer","Certificate","IPAddressPool","L2Advertisement"]

    def __init__(self, cfg: Config):
        self.cfg = cfg

    def apply(self):
        start("Apply Jinja bundle")
        yaml_all = render_all_in_one(self.cfg.render_ctx)
        _, extras = split_docs(yaml_all)

        def key_fn(obj):
            k = obj.get("kind","")
            try: return (self.ORDER.index(k), k)
            except ValueError: return (len(self.ORDER)+1, k)

        extras_sorted = sorted(extras, key=key_fn)
        results = []
        for obj in extras_sorted:
            kind = obj.get("kind","?")
            meta = obj.get("metadata") or {}
            name = meta.get("name","?")
            ns = meta.get("namespace")
            existed = subprocess.run(
                ["kubectl","-n",ns,"get",kind,name] if ns else ["kubectl","get",kind,name],
                capture_output=True
            ).returncode == 0
            raw = yaml.safe_dump(obj, sort_keys=False).encode()
            p = subprocess.run(["kubectl","apply","-f","-"], input=raw, capture_output=True)
            if p.returncode != 0:
                err = (p.stderr or b"").decode().strip()
                benign = any(x in err for x in (
                    "already exists",
                    "field is immutable",
                    "may not change once set",
                    "admission webhook"
                ))
                results.append((kind,name,"unchanged" if benign else "ERR","OK" if benign else "WARN"))
                continue
            results.append((kind,name,"unchanged" if existed else "created","OK"))

        ok = not any(s=="WARN" for *_, s in results)
        if RICH:
            table = Table(title="Bundle Summary", header_style="bold cyan")
            table.add_column("Kind", style="magenta")
            table.add_column("Name", style="green")
            table.add_column("Action", style="cyan")
            table.add_column("Status", style="bold")
            for k,n,a,s in results:
                table.add_row(k,n,a,"[green]OK[/green]" if s=="OK" else "[yellow]WARN[/yellow]")
            CON.print(Panel(table, title="Applied", border_style="cyan"))
        else:
            for r in results: print(" -", r)
        done("Apply Jinja bundle", ok)

# ---------- addons ----------
class Addons:
    def __init__(self, cfg: Config): self.cfg = cfg

    def _secret_exists(self, ns, name):
        return subprocess.run(["kubectl","-n",ns,"get","secret",name], capture_output=True).returncode==0
    def _cert_exists(self, ns, cert_name):
        return subprocess.run(["kubectl","-n",ns,"get","certificate",cert_name], capture_output=True).returncode==0

    def ensure_cert_manager(self):
        start("cert-manager install/verify")
        ns = self.cfg.namespaces["certmgr"]
        run(["helm","repo","add","jetstack","https://charts.jetstack.io"])
        run(["helm","repo","update"])
        if helmv(ns, self.cfg.rel_cert_mgr):
            print(f"[skip] cert-manager release {self.cfg.rel_cert_mgr} already present")
            done("cert-manager install/verify", True); return
        if kexists(ns,"deploy","cert-manager"):
            print("[detect] Existing cert-manager deployment (non-Helm) found; adopting without install.")
            self.cfg.cm_deploy = "cert-manager"
            self.cfg.cm_webhook = "cert-manager-webhook"
            self.cfg.cm_cainj = "cert-manager-cainjector"
            done("cert-manager install/verify", True); return
        for flags in (["--set","crds.enabled=true"], ["--set","installCRDs=true"]):
            try:
                run(["helm","upgrade","--install", self.cfg.rel_cert_mgr, "jetstack/cert-manager","-n", ns, "--create-namespace", *flags])
                done("cert-manager install/verify", True); return
            except subprocess.CalledProcessError:
                print(f"[warn] Helm install attempt with {' '.join(flags)} failed")
        if kexists(ns,"deploy","cert-manager"):
            print("[warn] Helm reported failure but cert-manager deployment exists; continuing.")
            self.cfg.cm_deploy = "cert-manager"
            self.cfg.cm_webhook = "cert-manager-webhook"
            self.cfg.cm_cainj = "cert-manager-cainjector"
            done("cert-manager install/verify", True); return
        print("❌ Failed to install or detect cert-manager")
        done("cert-manager install/verify", False); sys.exit(1)

    def wait_cert_manager(self, timeout=300):
        start("Wait cert-manager ready")
        t=time.time()
        while time.time()-t<timeout:
            try:
                for d in (self.cfg.cm_deploy,self.cfg.cm_webhook,self.cfg.cm_cainj):
                    run(["kubectl","-n",self.cfg.namespaces["certmgr"],"rollout","status",f"deploy/{d}","--timeout","10s"])
                done("Wait cert-manager ready", True); return True
            except subprocess.CalledProcessError: time.sleep(5)
        done("Wait cert-manager ready", False); return False

    def ensure_metallb(self):
        start("MetalLB install/verify")
        run(["helm","repo","add","metallb","https://metallb.github.io/metallb"])
        run(["helm","repo","update"])
        if helmv(self.cfg.namespaces["metallb"], self.cfg.rel_metallb):
            print(f"[skip] MetalLB release {self.cfg.rel_metallb} already present")
            done("MetalLB install/verify", True); return
        run(["helm","upgrade","--install", self.cfg.rel_metallb, "metallb/metallb",
             "-n", self.cfg.namespaces["metallb"], "--create-namespace"])
        done("MetalLB install/verify", True)

    def wait_metallb(self, timeout=300):
        start("Wait MetalLB ready")
        t=time.time()
        dep_candidates=[self.cfg.ml_ctrl,"controller","metallb-controller"]
        ds_candidates =[self.cfg.ml_speaker,"speaker","metallb-speaker"]
        while time.time()-t<timeout:
            dep_ok=ds_ok=False
            for n in dep_candidates:
                if subprocess.run(["kubectl","-n",self.cfg.namespaces["metallb"],"rollout","status",f"deploy/{n}","--timeout","10s"]).returncode==0:
                    dep_ok=True; break
            for n in ds_candidates:
                if subprocess.run(["kubectl","-n",self.cfg.namespaces["metallb"],"rollout","status",f"daemonset/{n}","--timeout","10s"]).returncode==0:
                    ds_ok=True; break
            if dep_ok and ds_ok:
                done("Wait MetalLB ready", True); return True
            time.sleep(5)
        done("Wait MetalLB ready", False); return False

    def ensure_ingress(self, service_type="LoadBalancer"):
        start("ingress-nginx install/verify")
        run(["helm","repo","add","ingress-nginx","https://kubernetes.github.io/ingress-nginx"])
        run(["helm","repo","update"])
        have_release = helmv(self.cfg.namespaces["ingress"], self.cfg.rel_ingress)
        have_secret  = self._secret_exists(self.cfg.namespaces["ingress"], self.cfg.wildcard_secret)
        cmd = [
            "helm","upgrade","--install", self.cfg.rel_ingress, "ingress-nginx/ingress-nginx",
            "-n", self.cfg.namespaces["ingress"], "--create-namespace",
            f"--set=controller.service.type={service_type}"
        ]
        if service_type == "LoadBalancer":
            cmd.append(f"--set=controller.service.loadBalancerIP={self.cfg.lb_static_ip}")
        if have_secret:
            cmd.append(f"--set=controller.extraArgs.default-ssl-certificate={self.cfg.namespaces['ingress']}/{self.cfg.wildcard_secret}")
        if have_release:
            print(f"[skip] ingress-nginx release {self.cfg.rel_ingress} already present (upgrade check)")
        run(cmd)
        done("ingress-nginx install/verify", True)

    def wait_ingress_ip(self, timeout=300):
        start("Wait ingress LB IP")
        t=time.time(); target=self.cfg.lb_static_ip
        while time.time()-t<timeout:
            try:
                ip=kget_jsonpath(self.cfg.namespaces["ingress"],"svc",self.cfg.ingress_svc,"{.status.loadBalancer.ingress[0].ip}")
                if ip==target:
                    print(f"[✓] Ingress LB IP set: {ip}")
                    done("Wait ingress LB IP", True); return True
            except subprocess.CalledProcessError: pass
            time.sleep(5)
        done("Wait ingress LB IP", False); return False

    def ensure_wildcard_cert(self):
        ns = self.cfg.namespaces["ingress"]
        secret = self.cfg.wildcard_secret
        cert_name = secret[:-4] if secret.endswith("-tls") else f"{self.cfg.name}-wildcard"
        if self._secret_exists(ns, secret): return
        if self._cert_exists(ns, cert_name): return
        start("Create wildcard Certificate")
        issuer = f"selfsigned-{self.cfg.name}"
        host_wc = f"*.{self.cfg.name}.{self.cfg.domain_suffix}"
        host_apx = f"{self.cfg.name}.{self.cfg.domain_suffix}"
        manifest = f"""
apiVersion: cert-manager.io/v1
kind: Issuer
metadata:
  name: {issuer}
  namespace: {ns}
spec:
  selfSigned: {{}}
---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: {cert_name}
  namespace: {ns}
spec:
  secretName: {secret}
  commonName: "{host_wc}"
  dnsNames:
    - "{host_apx}"
    - "{host_wc}"
  issuerRef:
    name: {issuer}
    kind: Issuer
""".strip()
        p = subprocess.run(["kubectl","apply","-f","-"], input=manifest.encode(), capture_output=True)
        ok = p.returncode == 0
        if not ok and RICH:
            CON.print(f"[yellow]Failed applying wildcard cert manifests: {(p.stderr or b'').decode().splitlines()[:1]}[/yellow]")
        done("Create wildcard Certificate", ok)

    def wait_tls_secret(self, namespace: str, name: str, timeout_sec: int = 120, poll: float = 3.0):
        phase = f"Wait TLS secret {namespace}/{name}"
        start(phase)
        if self._secret_exists(namespace, name):
            done(phase, True); return True
        dep_candidates = ["ingress-nginx-controller", f"{self.cfg.rel_ingress}-controller"]
        ingress_present = any(subprocess.run(["kubectl","-n",namespace,"get","deploy",d], capture_output=True).returncode==0 for d in dep_candidates)
        cert_guess = name[:-4] if name.endswith("-tls") else name
        cert_present = self._cert_exists(namespace, cert_guess)
        if not cert_present and not ingress_present:
            done(phase, True); return True
        until = time.time()+timeout_sec
        while time.time()<until:
            if self._secret_exists(namespace, name):
                done(phase, True); return True
            time.sleep(poll)
        done(phase, False); return False

    def probe(self):
        start("Probe connectivity")
        ip=self.cfg.lb_static_ip
        print(f"[info] Probing {ip} ...")
        print(f"    :80  {'open' if tcp_open(ip,80) else 'closed'}")
        print(f"    :443 {'open' if tcp_open(ip,443) else 'closed'}")
        try: print(f"[info] GET http://{ip} → {requests.get(f'http://{ip}', timeout=5).status_code}")
        except requests.RequestException as e: print(f"[warn] HTTP failed: {e}")
        try: print(f"[info] GET https://{ip} (insecure) → {requests.get(f'https://{ip}', timeout=5, verify=False).status_code}")
        except requests.RequestException as e: print(f"[warn] HTTPS failed: {e}")
        done("Probe connectivity", True)

    def maybe_fallback_to_nodeport(self, docker_provider: bool):
        if not docker_provider: return
        ip = self.cfg.lb_static_ip
        if tcp_open(ip,80) or tcp_open(ip,443): return
        if RICH: CON.print("[yellow]Docker provider: LB IP not reachable → switching ingress to NodePort.[/yellow]")
        self.ensure_ingress(service_type="NodePort")
        try:
            http_np = subprocess.check_output(
                ["kubectl","-n",self.cfg.namespaces["ingress"],"get","svc",self.cfg.ingress_svc,"-o","jsonpath={.spec.ports[?(@.port==80)].nodePort}"],
                text=True).strip()
            https_np = subprocess.check_output(
                ["kubectl","-n",self.cfg.namespaces["ingress"],"get","svc",self.cfg.ingress_svc,"-o","jsonpath={.spec.ports[?(@.port==443)].nodePort}"],
                text=True).strip()
            print(f"[info] Ingress NodePorts → HTTP={http_np}  HTTPS={https_np}")
        except Exception:
            pass

# ---------- NFS (all Python) ----------
class Nfs:
    def __init__(self, cfg: Config):
        self.cfg = cfg
        self.ns = "nfs-provisioner"
        self.release = "nfs-provisioner"
        self.sc_name = f"{cfg.name}-nfs"

    def ensure_packages(self):
        pkgs = [
            "cloud-guest-utils","lvm2","e2fsprogs","xfsprogs","btrfs-progs",
            "nfs-kernel-server","nfs-common"
        ]
        start("Host prep (packages)")
        run(sudoify(["apt-get","update","-y"]), check=False)
        run(sudoify(["apt-get","install","-y"] + pkgs), check=False)
        done("Host prep (packages)", True)

    def best_effort_expand_root_lvm(self):
        start("Host prep (LVM expand)")
        try:
            root_src = subprocess.check_output(["findmnt","-no","SOURCE","/"], text=True).strip()
            fs_type  = subprocess.check_output(["findmnt","-no","FSTYPE","/"], text=True).strip()
            if not root_src.startswith("/dev/mapper/"):
                print("[host] Root is not LVM; skipping LVM expansion.")
                done("Host prep (LVM expand)", True); return
            pv_line = subprocess.check_output(["pvs","--noheadings","-o","pv_name,vg_name"], text=True).strip().splitlines()[0]
            pv_dev  = pv_line.split()[0] if pv_line else ""
            if not pv_dev:
                print("[host] Could not determine PV device; skipping.")
                done("Host prep (LVM expand)", True); return
            part = os.path.basename(pv_dev)
            parent = subprocess.check_output(["lsblk","-no","PKNAME",pv_dev], text=True).strip()
            if not parent:
                print("[host] Could not determine parent disk; skipping.")
                done("Host prep (LVM expand)", True); return
            disk = f"/dev/{parent}"
            import re
            m = re.search(r"(\d+)$", part)
            if not m:
                print("[host] Could not find partition number; skipping.")
                done("Host prep (LVM expand)", True); return
            partnum = m.group(1)
            run(sudoify(["growpart", disk, partnum]), check=False)
            run(sudoify(["pvresize", pv_dev]), check=False)
            run(sudoify(["lvextend","-l","+100%FREE", root_src]), check=False)
            if fs_type == "ext4":
                run(sudoify(["resize2fs", root_src]), check=False)
            elif fs_type == "xfs":
                run(sudoify(["xfs_growfs","/"]), check=False)
            print("[host] LVM expansion attempted (best-effort).")
            done("Host prep (LVM expand)", True)
        except Exception as e:
            print(f"[host] LVM expansion skipped: {e}")
            done("Host prep (LVM expand)", True)

    def ensure_nfs_export(self, export_base: str, export_subdir: str, client_cidr: str):
        start("Host prep (NFS export)")
        try:
            run(sudoify(["mkdir","-p", export_subdir]), check=False)
            run(sudoify(["mkdir","-p", "/etc/exports.d"]), check=False)

            # Build allowed CIDRs: requested + Docker networks for this cluster
            allowed = [client_cidr]
            for c in discover_docker_subnets_for_cluster(self.cfg.name):
                if c not in allowed: allowed.append(c)

            # Prepare a dedicated exports file per cluster (overwrite idempotently)
            exports_file = f"/etc/exports.d/eks-{self.cfg.name}.exports"
            content = f"{export_subdir} " + " ".join(
                f"{cidr}(rw,sync,no_subtree_check,no_root_squash)" for cidr in allowed
            ) + "\n"

            # Clean up older generic file we may have created before
            old_generic = "/etc/exports.d/eks.exports"
            if os.path.exists(old_generic):
                run(sudoify(["rm","-f", old_generic]), check=False)

            if is_root():
                with open(exports_file, "w", encoding="utf-8") as f:
                    f.write(content)
            else:
                run(sudoify(["bash","-lc", f"cat > {shlex.quote(exports_file)} <<'EOF'\n{content}EOF"]), check=True)

            run(sudoify(["systemctl","enable","--now","nfs-kernel-server"]), check=False)
            run(sudoify(["exportfs","-rav"]), check=False)
            print(f"[host] Exported {export_subdir} → {' '.join(allowed)}")
            done("Host prep (NFS export)", True)
        except Exception as e:
            print(f"[host] NFS export setup failed: {e}")
            done("Host prep (NFS export)", False)

    def install_provisioner(self, server_ip: str, export_path: str):
        start("NFS provisioner install/verify")
        run(["helm","repo","add","nfs-subdir-external-provisioner","https://kubernetes-sigs.github.io/nfs-subdir-external-provisioner/"])
        run(["helm","repo","update"])
        if helmv(self.ns, self.release):
            print(f"[skip] NFS provisioner release {self.release} already present")
            done("NFS provisioner install/verify", True); return
        run([
            "helm","upgrade","--install", self.release,
            "nfs-subdir-external-provisioner/nfs-subdir-external-provisioner",
            "-n", self.ns, "--create-namespace",
            "--set", f"nfs.server={server_ip}",
            "--set", f"nfs.path={export_path}",
            "--set", f"storageClass.name={self.sc_name}",
            "--set", "storageClass.defaultClass=true",
            "--set", "storageClass.reclaimPolicy=Retain"
        ])
        done("NFS provisioner install/verify", True)

    def install_local_path(self):
        start("Install local-path-provisioner (fallback)")
        run(["helm","repo","add","rancher-lpp","https://rancher.github.io/local-path-provisioner/"])
        run(["helm","repo","update"])
        self.sc_name = f"{self.cfg.name}-local"
        run([
            "helm","upgrade","--install","local-path-provisioner","rancher-lpp/local-path-provisioner",
            "-n","local-path-storage","--create-namespace",
            "--set",f"storageClass.name={self.sc_name}",
            "--set","storageClass.defaultClass=true"
        ])
        done("Install local-path-provisioner (fallback)", True)

    def wait_ready(self, timeout=300):
        start("Wait NFS provisioner ready")
        dep_name = ""
        try:
            dep_name = subprocess.check_output([
                "kubectl","-n",self.ns,"get","deploy",
                "-l","app.kubernetes.io/instance="+self.release,
                "-o","jsonpath={.items[0].metadata.name}"
            ], text=True).strip()
        except Exception:
            pass
        if not dep_name:
            dep_name = "nfs-provisioner-nfs-subdir-external-provisioner"  # Helm's default name
        t=time.time()
        while time.time()-t<timeout:
            try:
                avail = subprocess.check_output(
                    ["kubectl","-n",self.ns,"get","deploy",dep_name,"-o","jsonpath={.status.availableReplicas}"],
                    text=True
                ).strip()
                if avail and int(avail) >= 1:
                    done("Wait NFS provisioner ready", True); return True
            except Exception:
                pass
            time.sleep(5)
        # surface current status to help debugging
        run(["kubectl","-n",self.ns,"get","pods","-o","wide"], check=False)
        done("Wait NFS provisioner ready", False); return False

    def verify_storageclass(self):
        start("Verify StorageClass default")
        run(["kubectl","get","sc"], check=False)
        try:
            ann = subprocess.check_output([
                "kubectl","get","sc",self.sc_name,"-o",
                "jsonpath={.metadata.annotations.storageclass\\.kubernetes\\.io/is-default-class}"
            ], text=True).strip()
            print(f"[info] {self.sc_name} is-default-class: {ann or '<unset>'}")
        except Exception:
            print("[warn] Could not read default-class annotation")
        done("Verify StorageClass default", True)

# ---------- overview ----------
def print_cluster_overview():
    banner("Cluster overview")
    run(["kubectl","get","nodes"], check=False)
    run(["kubectl","get","sc"], check=False)
    run(["kubectl","get","svc","-A","-o","wide"], check=False)
    run(["kubectl","get","ingress","-A"], check=False)
    run(["kubectl","get","pods","-A","--field-selector","status.phase!=Succeeded"], check=False)

# ---------- main ----------
NFS_BASE_STATIC = "/srv/eks"
CLIENT_CIDR_STATIC = "***********/24"
CLUSTER_NAME_STATIC = "dev"

def main():
    banner("Preflight"); ensure_tools(); ensure_eksctl_anywhere(); ensure_docker_running()

    banner("Inputs")
    name    = CLUSTER_NAME_STATIC
    domain  = prompt("Enter domain suffix", "home.lan")
    lb_pool = prompt("Enter MetalLB pool range", "*************-*************")
    lb_ip   = prompt("Enter static LoadBalancer IP", "*************")
    nfs_base    = NFS_BASE_STATIC
    client_cidr = CLIENT_CIDR_STATIC
    try:
        default_host_ip = socket.gethostbyname(socket.gethostname())
    except Exception:
        default_host_ip = "127.0.0.1"
    host_ip = prompt("Enter host LAN IP (serves NFS)", default_host_ip)

    if RICH:
        tbl = Table(title="Input Summary", header_style="bold cyan")
        tbl.add_column("Key"); tbl.add_column("Value")
        for k,v in ("cluster",name), ("domain",domain), ("pool",lb_pool), ("lb_ip",lb_ip), ("nfs_base",nfs_base), ("client_cidr",client_cidr), ("host_ip",host_ip):
            tbl.add_row(k,v)
        CON.print(tbl)

    cfg = Config(name, domain, lb_pool, lb_ip)
    nfs = Nfs(cfg)
    export_base   = os.path.abspath(nfs_base)
    export_subdir = os.path.join(export_base, name)

    # Host prep (all Python)
    nfs.ensure_packages()
    nfs.best_effort_expand_root_lvm()
    nfs.ensure_nfs_export(export_base, export_subdir, client_cidr)

    banner("Cluster detection / creation")
    detect_or_set_kubeconfig(name)
    if not kube_reachable():
        print("[info] No cluster detected. Creating a new EKS-Anywhere (Docker) cluster...")
        create_eksa_docker_cluster(cluster_name=name, ctx_for_template=cfg.render_ctx)
    else:
        print("[info] Kubernetes reachable.")

    print_cluster_overview()

    banner("Configuration")
    selected_ip = choose_available_ip(cfg.lb_static_ip, cfg.lb_ip_pool)
    if not selected_ip:
        print(f"❌ No free IPs in pool {cfg.lb_ip_pool}. Free an address or change the pool.")
        summary(); sys.exit(1)
    if selected_ip != cfg.lb_static_ip:
        print(f"[info] Using {selected_ip} instead of requested {cfg.lb_static_ip}")
        cfg.lb_static_ip = selected_ip

    banner("Add-ons & base wiring")
    addons = Addons(cfg)
    addons.ensure_cert_manager(); addons.wait_cert_manager()
    addons.ensure_metallb(); addons.wait_metallb()
    ExtrasFromTemplate(cfg).apply()
    addons.ensure_wildcard_cert(); addons.wait_tls_secret("ingress-nginx", f"wildcard-{name}-tls", timeout_sec=120)
    addons.ensure_ingress(service_type="LoadBalancer"); addons.wait_ingress_ip()
    addons.probe()
    addons.maybe_fallback_to_nodeport(docker_provider=is_docker_provider(name))

    banner("Persistent Storage (NFS)")
    nfs.install_provisioner(server_ip=host_ip, export_path=export_subdir)
    if not nfs.wait_ready(timeout=180) and is_docker_provider(name):
        try: run(["helm","-n",nfs.ns,"uninstall",nfs.release], check=False)
        except Exception: pass
        nfs.install_local_path(); nfs.wait_ready(timeout=120)
    nfs.verify_storageclass()

    banner("Done")
    print(f"Name                : {cfg.name}")
    print(f"Ingress IP          : {cfg.lb_static_ip}")
    print(f"Wildcard covers     : *.{cfg.root_zone}")
    print(f"Default TLS secret  : {cfg.namespaces['ingress']}/{cfg.wildcard_secret}")
    print(f"NFS export          : {export_subdir}")
    print(f"NFS server IP       : {host_ip}")
    print(f"Default StorageClass: {nfs.sc_name}")
    summary()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n[info] Interrupted"); summary(); sys.exit(130)
