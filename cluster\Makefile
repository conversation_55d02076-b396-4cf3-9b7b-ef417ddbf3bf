MODE ?= kubernetes
DISK ?= /dev/xvdb

all: prerequisites cloud-init disable-swap nfs-storage

prerequisites:
	@echo "Updating packages..."
	@apt-get update -qq
	@apt-get upgrade -y docker.io || true
	@apt-get install -y python3-pip || true
	@apt-get install -y containerd git curl wget jq python3 python3-pip docker.io nfs-common xfsprogs e2fsprogs parted xe-guest-utilities cloud-initramfs-growroot || true
	@systemctl enable --now docker || true
	@usermod -aG docker $$SUDO_USER || true
	@mkdir -p /etc/containerd
	@containerd config default > /etc/containerd/config.toml || echo 'version = 2\n[plugins."io.containerd.grpc.v1.cri".containerd.runtimes.runc]\n  runtime_type = "io.containerd.runc.v2"\n  [plugins."io.containerd.grpc.v1.cri".containerd.runtimes.runc.options]\n    SystemdCgroup = true' > /etc/containerd/config.toml
	@sed -i 's/SystemdCgroup = false/SystemdCgroup = true/' /etc/containerd/config.toml || true
	@systemctl enable --now containerd systemd-timesyncd || true
	@echo -e 'overlay\nbr_netfilter' > /etc/modules-load.d/k8s.conf
	@modprobe overlay || true
	@modprobe br_netfilter || true
	@echo -e 'net.ipv4.ip_forward=1\nnet.bridge.bridge-nf-call-iptables=1\nnet.bridge.bridge-nf-call-ip6tables=1' > /etc/sysctl.d/k8s.conf
	@sysctl --system || true
	@if [ "$(MODE)" = "eks-anywhere" ]; then \
		curl -fsSL -o /tmp/get_helm.sh https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3; \
		chmod +x /tmp/get_helm.sh; bash /tmp/get_helm.sh; rm -f /tmp/get_helm.sh; \
		curl -sL -o /tmp/eksctl.tar.gz https://github.com/weaveworks/eksctl/releases/latest/download/eksctl_Linux_amd64.tar.gz; \
		tar -xzf /tmp/eksctl.tar.gz -C /tmp; install -m 0755 /tmp/eksctl /usr/local/bin/eksctl; rm -f /tmp/eksctl.tar.gz /tmp/eksctl; \
		MANIFEST=$$(curl -sSL https://anywhere-assets.eks.amazonaws.com/releases/eks-a/manifest.yaml); \
		VERSION=$$(echo "$$MANIFEST" | yq .spec.latestVersion); \
		URL=$$(echo "$$MANIFEST" | yq '.spec.releases[] | select(.version=="'$$VERSION'").eksABinary.linux.uri'); \
		curl -sSL $$URL -o /tmp/eksctl-anywhere.tar.gz; tar -xzf /tmp/eksctl-anywhere.tar.gz -C /tmp eksctl-anywhere; \
		install -m 0755 /tmp/eksctl-anywhere /usr/local/bin/eksctl-anywhere; rm -f /tmp/eksctl-anywhere.tar.gz /tmp/eksctl-anywhere; \
	fi
	@VERSION=$$(curl -L -s https://dl.k8s.io/release/stable.txt); \
	curl -L -o /usr/local/bin/kubectl https://dl.k8s.io/release/$$VERSION/bin/linux/amd64/kubectl || true; \
	chmod +x /usr/local/bin/kubectl || true
	@wget -qO /usr/local/bin/yq https://github.com/mikefarah/yq/releases/latest/download/yq_linux_amd64 || true
	@chmod +x /usr/local/bin/yq || true

cloud-init:
	@echo "datasource_list: [NoCloud, ConfigDrive, OpenStack, XenStore]" > /etc/cloud/cloud.cfg.d/99-xcp-ng.cfg
	@dpkg-reconfigure --default-priority cloud-init || true
	@chmod a-x /etc/cloud/clean.d/99-installer || true
	@rm -f /etc/cloud/cloud.cfg.d/99-installer.cfg /etc/cloud/cloud.cfg.d/subiquity-disable-cloudinit-networking.cfg /etc/cloud/cloud.cfg.d/90-installer-network.cfg || true
	@cloud-init clean --logs --seed || true
	@rm -rf /var/lib/cloud/instances /var/lib/cloud/instance || true
	@rm -f /var/log/cloud-init.log /var/log/cloud-init* /etc/netplan/00-installer-config.yaml /etc/netplan/50-cloud-init.yaml || true
	@systemctl enable cloud-init-local.service cloud-config.service cloud-final.service || true
	@systemctl enable --now xe-linux-distribution || systemctl enable --now xe-daemon || true

disable-swap:
	@swapoff -a || true
	@sed -i '/swap/d' /etc/fstab || true

nfs-storage:
	@if [ -b $(DISK) ] && ! mountpoint -q /srv/eks; then \
		parted $(DISK) --script mklabel gpt mkpart primary ext4 0% 100%; \
		mkfs.ext4 -L eksdata $(DISK)1; \
		mkdir -p /srv/eks; \
		UUID=$$(blkid -s UUID -o value $(DISK)1); \
		echo "UUID=$$UUID /srv/eks ext4 defaults,noatime 0 2" >> /etc/fstab; \
		mount -a || true; \
	fi
	@mkdir -p /srv/eks/dev
	@chown -R nobody:nogroup /srv/eks
	@chmod -R 0777 /srv/eks
	@apt-get install -y nfs-kernel-server
	@sed -i '/\/srv\/eks\/dev /d' /etc/exports || true
	@echo "/srv/eks/dev ***********/24(rw,sync,no_subtree_check,no_root_squash)" >> /etc/exports
	@exportfs -ra
	@systemctl enable --now nfs-kernel-server
	@showmount -e localhost || true