import paramiko
import getpass

# === CONFIG ===
XCPNG_HOST = "*************"
XCPNG_USER = "root"
XCPNG_PASS = getpass.getpass("Enter XCP-ng root password: ")
XOA_VM_NAME = "XOA"

# === SSH Command Helper ===
def run_ssh_command(client, command):
    stdin, stdout, stderr = client.exec_command(command)
    output = stdout.read().decode().strip()
    error = stderr.read().decode().strip()
    if error and "Warning" not in error:
        raise RuntimeError(f"Command error: {error}")
    return output

# === MAIN ===
def main():
    # Prompt for new password
    new_password = getpass.getpass("Enter the new system password for user 'xoa': ")

    # SSH in
    print(f"🔗 Connecting to XCP-ng host {XCPNG_HOST}...")
    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    ssh.connect(XCPNG_HOST, username=XCPNG_USER, password=XCPNG_PASS)

    # Get UUID of XOA VM
    print("🔍 Finding XOA VM UUID...")
    uuid_cmd = f"xe vm-list name-label=\"{XOA_VM_NAME}\" params=uuid --minimal"
    xoa_uuid = run_ssh_command(ssh, uuid_cmd)
    if not xoa_uuid:
        raise RuntimeError(f"❌ Could not find VM with name-label '{XOA_VM_NAME}'")

    print(f"✅ Found XOA VM UUID: {xoa_uuid}")

    # Set xenstore password
    set_pw_cmd = f"xe vm-param-set uuid={xoa_uuid} xenstore-data:vm-data/system-account-xoa-password='{new_password}'"
    run_ssh_command(ssh, set_pw_cmd)
    print("🔐 Password set via xenstore.")

    # Reboot XOA VM to apply change
    reboot_cmd = f"xe vm-reboot uuid={xoa_uuid}"
    run_ssh_command(ssh, reboot_cmd)
    print("🔁 XOA VM rebooting...")

    print("\n✅ You can now log in to the XOA console with:")
    print("   username: xoa")
    print("   password: [the one you just set]")

    ssh.close()

if __name__ == "__main__":
    main()