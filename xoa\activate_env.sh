#!/bin/bash

# Activate the micromamba environment
MICROMAMBA_DIR="/opt/micromamba"
MICROMAMBA_BIN="${MICROMAMBA_DIR}/bin/micromamba"
ENV_NAME="xcp_storage_env"

# Initialize micromamba shell integration
eval "$($MICROMAMBA_BIN shell hook --shell=bash)"

# Activate the environment
micromamba activate $ENV_NAME

echo "[INFO] Environment '$ENV_NAME' activated"
echo "[INFO] You can now run your Python scripts directly"

# Start a new bash shell with the environment activated
exec bash