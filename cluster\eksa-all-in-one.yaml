# Supporting objects (namespaces, MetalLB, wildcard TLS) — rendered by cluster_setup.py

apiVersion: v1
kind: Namespace
metadata:
  name: metallb-system
---
apiVersion: v1
kind: Namespace
metadata:
  name: cert-manager
---
apiVersion: v1
kind: Namespace
metadata:
  name: ingress-nginx
---
apiVersion: metallb.io/v1beta1
kind: IPAddressPool
metadata:
  name: "{{ CLUSTER_NAME }}-pool"
  namespace: metallb-system
spec:
  addresses:
    - "{{ LB_POOL }}"
---
apiVersion: metallb.io/v1beta1
kind: L2Advertisement
metadata:
  name: "{{ CLUSTER_NAME }}-adv"
  namespace: metallb-system
spec:
  ipAddressPools:
    - "{{ CLUSTER_NAME }}-pool"
---
# Self-signed issuer & wildcard certificate (dev convenience)
apiVersion: cert-manager.io/v1
kind: Issuer
metadata:
  name: selfsigned-{{ CLUSTER_NAME }}
  namespace: ingress-nginx
spec:
  selfSigned: {}
---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: wildcard-{{ CLUSTER_NAME }}
  namespace: ingress-nginx
spec:
  secretName: wildcard-{{ CLUSTER_NAME }}-tls
  commonName: "*.{{ CLUSTER_NAME }}.{{ DOMAIN }}"
  dnsNames:
    - "{{ CLUSTER_NAME }}.{{ DOMAIN }}"
    - "*.{{ CLUSTER_NAME }}.{{ DOMAIN }}"
  issuerRef:
    name: selfsigned-{{ CLUSTER_NAME }}
    kind: Issuer

---
# EKS Anywhere (Docker provider) — rendered by cluster_setup.py
apiVersion: anywhere.eks.amazonaws.com/v1alpha1
kind: Cluster
metadata:
  name: {{ CLUSTER_NAME }}
spec:
  kubernetesVersion: "{{ KUBERNETES_VERSION }}"
  clusterNetwork:
    cniConfig:
      cilium: {}
    pods:
      cidrBlocks: ["{{ POD_CIDR }}"]
    services:
      cidrBlocks: ["{{ SERVICE_CIDR }}"]
  controlPlaneConfiguration:
    count: {{ CONTROL_PLANE_COUNT }}
  workerNodeGroupConfigurations:
    - name: md-0
      count: {{ WORKER_COUNT }}
  datacenterRef:
    kind: DockerDatacenterConfig
    name: {{ CLUSTER_NAME }}
  externalEtcdConfiguration:
    count: {{ EXTERNAL_ETCD_COUNT }}
  managementCluster:
    name: {{ CLUSTER_NAME }}
---
apiVersion: anywhere.eks.amazonaws.com/v1alpha1
kind: DockerDatacenterConfig
metadata:
  name: {{ CLUSTER_NAME }}
spec: {}
