#cloud-config
hostname: node-01
fqdn: node-01.local
preserve_hostname: false
ssh_pwauth: true

# keep/ensure the default 'ubuntu' user
users:
  - name: ubuntu
    groups: sudo
    shell: /bin/bash
    lock_passwd: false

# ✅ clear-text password (same method you confirmed works)
chpasswd:
  encrypted: false
  expire: false
  list: |
    ubuntu:test1234

# do not let cloud-init write its own DHCP netplan
network:
  config: disabled

package_update: true
packages:
  - avahi-daemon
  - libnss-mdns
  - openssh-server

write_files:
  # DHCP netplan: primary NIC preferred default route, secondary no default route
  - path: /etc/netplan/99-dhcp.yaml
    owner: root:root
    permissions: '0600'
    content: |
      network:
        version: 2
        renderer: networkd
        ethernets:
          primary:
            match: { name: "en*0" }        # first NIC (mgmt/API)
            dhcp4: true
            optional: true
            dhcp4-overrides:
              route-metric: 100
              send-hostname: true
          secondary:
            match: { name: "en*1" }        # second NIC (storage/overlay)
            dhcp4: true
            optional: true
            dhcp4-overrides:
              use-routes: false
  - path: /etc/systemd/resolved.conf.d/mdns.conf
    owner: root:root
    permissions: '0644'
    content: |
      [Resolve]
      MulticastDNS=yes
      LLMNR=no
  - path: /etc/nsswitch.conf
    owner: root:root
    permissions: '0644'
    content: |
      passwd:         files systemd
      group:          files systemd
      shadow:         files
      gshadow:        files
      hosts:          files mdns4_minimal [NOTFOUND=return] dns myhostname
      networks:       files
      protocols:      db files
      services:       db files
      ethers:         db files
      rpc:            db files
      netgroup:       nis

bootcmd:
  - [ bash, -lc, 'rm -f /etc/netplan/50-cloud-init.yaml || true' ]

runcmd:
  - [ bash, -lc, 'mkdir -p /etc/systemd/resolved.conf.d' ]
  - [ bash, -lc, 'systemctl enable --now systemd-networkd systemd-resolved avahi-daemon' ]
  - [ bash, -lc, 'netplan --debug generate' ]
  - [ bash, -lc, 'netplan apply' ]
  - [ bash, -lc, 'systemctl restart ssh || true' ]
