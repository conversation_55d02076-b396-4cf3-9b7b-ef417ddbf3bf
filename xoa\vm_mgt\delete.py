# delete.py
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
from shared import *

async def delete_vms(client: XOClient, inventory: Inventory):
    # Hide protected VMs from the deletion list
    vms = [v for v in inventory.list_vms() if v.get("id") not in PROTECTED_VM_IDS]
    selected_vms = await multi_pick(
        vms,
        lambda v: f"{v.get('name_label')}  (ID {v.get('id')})",
        "Pick VMs to Delete (Space to select, Enter to confirm)"
    )
    if not selected_vms:
        print("Cancelled.")
        return

    # Final safety: remove any protected ID if somehow selected
    selected_vms = [v for v in selected_vms if v.get("id") not in PROTECTED_VM_IDS]
    if not selected_vms:
        print("🚫 All selected VMs are protected. Nothing to delete.")
        return

    names = ", ".join(v.get("name_label") for v in selected_vms)
    if not confirm(f"Delete {len(selected_vms)} VM(s): {names}? This will delete disks."):
        print("🚫 Deletion cancelled.")
        return

    # Delete each selected VM, report per-VM result
    for v in selected_vms:
        vm_id = v.get("id")
        vm_name = v.get("name_label")
        try:
            await client.call("vm.delete", {"id": vm_id, "deleteDisks": True})
            print(f"✅ VM '{vm_name}' deleted with disks.")
        except Exception as e:
            print(f"⚠ Failed to delete VM '{vm_name}': {e}")

if __name__ == "__main__":
    async def standalone_main():
        async with XOClient(XO_HOST, XO_USER, KEYRING_SERVICE) as client:
            inventory = Inventory(client)
            await inventory.refresh()
            await delete_vms(client, inventory)

    try:
        asyncio.run(standalone_main())
    except KeyboardInterrupt:
        print("\nCancelled.")