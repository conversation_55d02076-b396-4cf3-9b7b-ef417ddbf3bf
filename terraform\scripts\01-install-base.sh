#!/usr/bin/env bash
set -euo pipefail
set -x

# ---- env ----
export DEBIAN_FRONTEND=noninteractive
export PIP_NO_CACHE_DIR=1
export PYTHONDONTWRITEBYTECODE=1
export TF_PLUGIN_CACHE_DIR=/opt/tf-cache

# Create terraform plugin cache directory early
mkdir -p "${TF_PLUGIN_CACHE_DIR}"
chmod 755 "${TF_PLUGIN_CACHE_DIR}"

# ---- base deps ----
apt-get update
apt-get install -y --no-install-recommends \
  python3 python3-pip python3-dev python3-venv \
  build-essential curl gnupg ca-certificates unzip jq \
  gcc g++ make tree
rm -rf /var/lib/apt/lists/*

# ---- terraform (apt with fallback) ----
if curl -fsSL https://apt.releases.hashicorp.com/gpg | gpg --dearmor -o /usr/share/keyrings/hashicorp-archive-keyring.gpg; then
  echo "deb [signed-by=/usr/share/keyrings/hashicorp-archive-keyring.gpg] https://apt.releases.hashicorp.com $(. /etc/os-release && echo "$VERSION_CODENAME") main" \
    > /etc/apt/sources.list.d/hashicorp.list
  apt-get update
  apt-get install -y --no-install-recommends terraform
  rm -rf /var/lib/apt/lists/*
else
  TF_VERSION="$(curl -fsSL https://checkpoint-api.hashicorp.com/v1/check/terraform | jq -r .current_version)"
  curl -fsSLo /tmp/terraform.zip "https://releases.hashicorp.com/terraform/${TF_VERSION}/terraform_${TF_VERSION}_linux_amd64.zip"
  unzip -d /usr/local/bin /tmp/terraform.zip && rm -f /tmp/terraform.zip
fi
terraform version

# ---- cdktf cli ----
npm install -g npm@latest
npm install -g --omit=dev cdktf-cli@latest || npm install -g cdktf-cli@latest
npm cache clean --force
cdktf --version

# ---- python deps (global, no venv) ----
python3 -m pip install --break-system-packages --upgrade pip setuptools wheel
python3 -m pip install --break-system-packages \
  constructs \
  cdktf \
  jsii \
  cdktf-cdktf-provider-kubernetes \
  cdktf-cdktf-provider-helm \
  --upgrade
python3 --version

# ---- provider cache prewarm ----
mkdir -p "${TF_PLUGIN_CACHE_DIR}"
chmod 755 "${TF_PLUGIN_CACHE_DIR}"

# Resolve tf-mirror directory (script-relative first, then fallbacks)
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CANDIDATES=(
  "${SCRIPT_DIR}/../tf-mirror"            # proxmox/terraform/tf-mirror relative to scripts
  "/workspace/proxmox/terraform/tf-mirror"
  "/workspace/tf-mirror"
  "/opt/tf-mirror"
  "/tmp/tf-mirror"
)
TF_MIRROR_PATH=""
for p in "${CANDIDATES[@]}"; do
  if [ -d "$p" ]; then
    TF_MIRROR_PATH="$p"
    break
  fi
done

if [ -n "${TF_MIRROR_PATH}" ]; then
  terraform -chdir="${TF_MIRROR_PATH}" init -backend=false || echo "tf-mirror init failed (non-fatal)"
  terraform -chdir="${TF_MIRROR_PATH}" providers mirror "${TF_PLUGIN_CACHE_DIR}" || echo "tf-mirror provider mirror failed (non-fatal)"
else
  echo "Warning: tf-mirror directory not found, skipping provider cache prewarm"
fi

# ---- tiny helper for project-specific warmups (optional) ----
cat > /usr/local/bin/cdktf-warmup <<'EOSH'
#!/usr/bin/env bash
set -euo pipefail
cdktf synth || true
EOSH
chmod +x /usr/local/bin/cdktf-warmup
