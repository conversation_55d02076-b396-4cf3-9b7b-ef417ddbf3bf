# deploy.py
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from shared import *

class VMDeployer:
    """Handles checkpoint, prompts, summary, vm.create, and auto-start."""
    def __init__(self, client: XOClient, inventory: Inventory, repo: ConfigRepo):
        self.client = client
        self.inventory = inventory
        self.repo = repo

    # --------- Checkpoint helpers ---------
    def _save_checkpoint(self, data: Dict[str, Any]) -> None:
        try:
            CHECKPOINT_PATH.write_text(json.dumps(data, indent=2))
        except Exception as e:
            print(f"⚠ Could not save checkpoint: {e}")

    def _save_checkpoint_backup(self, data: Dict[str, Any]) -> None:
        """Write a timestamped backup of the checkpoint for easy rollback/edits."""
        try:
            ts = datetime.now().strftime("%Y%m%d-%H%M%S")
            backup = CHECKPOINT_PATH.with_name(f"{CHECKPOINT_PATH.stem}.{ts}{CHECKPOINT_PATH.suffix}")
            backup.write_text(json.dumps(data, indent=2))
            print(f"💾 Checkpoint backup saved: {backup.name}")
        except Exception as e:
            print(f"⚠ Could not save checkpoint backup: {e}")

    def _load_checkpoint(self) -> Optional[Dict[str, Any]]:
        if not CHECKPOINT_PATH.exists():
            return None
        try:
            return json.loads(CHECKPOINT_PATH.read_text())
        except Exception as e:
            print(f"⚠ Could not read checkpoint: {e}")
            return None

    @staticmethod
    def _extract_id(result) -> str:
        """XO may return {'id': '<uuid>'} or just '<uuid>'."""
        if isinstance(result, dict) and "id" in result:
            return str(result["id"])
        return str(result)

    # --------- Helpers: local J2 rendering ---------
    def _render_j2_cloud_config(self, template_path: Path, values: Dict[str, str]) -> str:
        from jinja2 import Environment, FileSystemLoader, StrictUndefined, TemplateError
        env = Environment(
            loader=FileSystemLoader(str(template_path.parent)),
            undefined=StrictUndefined,
            trim_blocks=True,
            lstrip_blocks=True,
        )
        try:
            tpl = env.get_template(template_path.name)
            return tpl.render(**values)
        except TemplateError as e:
            raise RuntimeError(f"Failed to render template {template_path}: {e}")

    # --------- Main flow ---------
    async def run(self):
        # Refresh objects
        await self.inventory.refresh()

        # Load saved configs once
        user_configs, network_configs = await self.repo.fetch()

        # Try checkpoint
        ckpt = self._load_checkpoint()
        use_ckpt = False
        if ckpt:
            print("\n💾 Found previous configuration checkpoint.")
            use_ckpt = ask("Use previous configuration? [Y/n]", "Y").lower() in ("y", "yes")

        if use_ckpt:
            # Resolve template from checkpoint
            template_id = ckpt.get("template_id")
            tmpl = self.inventory.objects.get(template_id)
            if not tmpl:
                print("⚠ Template in checkpoint not found; falling back to selection.")
                tmpl = await self._select_template()
                if not tmpl:
                    print("Cancelled.")
                    return
                template_id = tmpl["id"]
            else:
                print(f"📦 Using template from checkpoint: {tmpl.get('name_label')} ({template_id})")

            # VM Name (still editable)
            default_vm_from_template = strip_prefix(tmpl.get("name_label", "vm-from-template") or "", NAME_PREFIX)
            default_vm_name = ckpt.get("vm_name") or default_vm_from_template or "vm-from-template"
            vm_name = ask("VM Name", default_vm_name)

            # CPUs/RAM from checkpoint
            vcpus = int(ckpt.get("vcpus", DEFAULT_VCPUS))
            ram_gib = int(ckpt.get("ram_gib", DEFAULT_RAM_GIB))
            memory_bytes = bytes_from_gib(ram_gib)

            # Network config from checkpoint (optional)
            net_cfg = None
            net_cfg_id = ckpt.get("network_config_id")
            if net_cfg_id:
                net_cfg = self.repo.find_by_id(network_configs, net_cfg_id)
                if not net_cfg:
                    print("⚠ Network config in checkpoint not found; skipping.")

            # Always use fixed J2 path; prompt if missing
            tpl_path = FIXED_J2_PATH
            if not tpl_path.exists():
                print(f"⚠ Fixed J2 template not found at {tpl_path}. Please provide a valid path.")
                tpl_path = ask_path("Path to J2 template", DEFAULT_J2_PATH)
                while not tpl_path.exists():
                    print(f"Template not found: {tpl_path}")
                    tpl_path = ask_path("Path to J2 template", DEFAULT_J2_PATH)
            tpl_path = tpl_path.resolve()
            print(f"🧩 Using J2 template: {tpl_path}")
            j2_defaults = extract_j2_defaults(tpl_path)

            hostname = ask("Hostname", (ckpt.get("hostname") or j2_defaults.get("hostname") or vm_name))
            fqdn = f"{hostname}.local"
            username = ask("Username", (ckpt.get("username") or j2_defaults.get("username") or "admin"))

            password = getpass.getpass(f"Password for {username}: ")
            values = {"hostname": hostname, "fqdn": fqdn, "username": username, "password": password}
            cloud_config_text = self._render_j2_cloud_config(tpl_path, values)
            user_cfg_name = f"J2: {tpl_path}"

            # Build and save updated checkpoint + backup
            ckpt_payload: Dict[str, Any] = {
                "template_id": template_id,
                "vm_name": vm_name,
                "vcpus": vcpus,
                "ram_gib": ram_gib,
                "use_j2": True,
                "j2_path": str(tpl_path),
                "hostname": hostname,
                "username": username,
                "user_config_id": None,
                "network_config_id": (net_cfg.get("id") if net_cfg else None),
            }
            self._save_checkpoint(ckpt_payload)
            self._save_checkpoint_backup(ckpt_payload)

            # Rebuild VIFs from the (current) template
            vifs = self.inventory.resolve_template_vifs(tmpl)
            await self._summary_and_deploy(
                tmpl, template_id, vm_name, vm_name, vcpus, ram_gib, memory_bytes,
                vifs, user_cfg_name, cloud_config_text, net_cfg
            )

        else:
            # Fresh interactive flow
            tmpl = await self._select_template()
            if not tmpl:
                print("Cancelled.")
                return
            template_id = tmpl["id"]
            default_vm_name = strip_prefix(tmpl.get("name_label", "vm-from-template") or "", NAME_PREFIX)
            vm_name = ask("VM Name", default_vm_name or "vm-from-template")
            vm_desc = vm_name

            vcpus = ask_int("vCPUs", DEFAULT_VCPUS)
            ram_gib = ask_int("RAM GiB", DEFAULT_RAM_GIB)
            memory_bytes = bytes_from_gib(ram_gib)

            # Always use fixed J2 template; prompt only if missing
            tpl_path = FIXED_J2_PATH
            if not tpl_path.exists():
                print(f"⚠ Fixed J2 template not found at {tpl_path}. Please provide a valid path.")
                tpl_path = ask_path("Path to J2 template", DEFAULT_J2_PATH)
                while not tpl_path.exists():
                    print(f"Template not found: {tpl_path}")
                    tpl_path = ask_path("Path to J2 template", DEFAULT_J2_PATH)
            tpl_path = tpl_path.resolve()
            j2_defaults = extract_j2_defaults(tpl_path)

            hostname = ask("Hostname", (j2_defaults.get("hostname") or vm_name))
            fqdn = f"{hostname}.local"
            username = ask("Username", (j2_defaults.get("username") or "admin"))

            password = getpass.getpass(f"Password for {username}: ")
            values = {"hostname": hostname, "fqdn": fqdn, "username": username, "password": password}
            cloud_config_text = self._render_j2_cloud_config(tpl_path, values)
            user_cfg_name = f"J2: {tpl_path}"

            ckpt_payload: Dict[str, Any] = {
                "template_id": template_id,
                "vm_name": vm_name,
                "vcpus": vcpus,
                "ram_gib": ram_gib,
                "use_j2": True,
                "j2_path": str(tpl_path),
                "hostname": hostname,
                "username": username,
                "user_config_id": None,
                "network_config_id": None,
            }

            net_cfg = await self._select_network_config(network_configs)  # optional
            if net_cfg:
                ckpt_payload["network_config_id"] = net_cfg.get("id")

            vifs = self.inventory.resolve_template_vifs(tmpl)

            # Save checkpoint and a timestamped backup (no password stored)
            self._save_checkpoint(ckpt_payload)
            self._save_checkpoint_backup(ckpt_payload)

            await self._summary_and_deploy(
                tmpl, template_id, vm_name, vm_desc, vcpus, ram_gib, memory_bytes,
                vifs, user_cfg_name, cloud_config_text, net_cfg
            )

    # --------- Sub-steps ---------
    async def _select_template(self) -> Optional[Dict[str, Any]]:
        templates = self.inventory.list_templates(NAME_PREFIX)
        return term_pick(templates, lambda t: f"{t.get('name_label')}  (ID {t.get('id')})", "📦 Pick a Template")

    async def _select_network_config(self, network_configs: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        return term_pick(network_configs, lambda c: f"{c['name']}  (network-config)", "🌐 Pick Network Config (optional; press Enter to skip)", allow_skip=True)

    async def _set_vm_memory(self, vm_id: str, memory_bytes: int) -> None:
        """Adjust VM memory limits post-create in a safe order (best-effort)."""
        target = int(memory_bytes)

        # Helper to get the latest VM object
        async def _get_vm() -> Dict[str, Any]:
            try:
                objs = await self.client.call("xo.getAllObjects")
                return objs.get(vm_id, {}) or {}
            except Exception:
                return {}

        def _get_int(vm: Dict[str, Any], *keys: str) -> Optional[int]:
            for k in keys:
                v = vm.get(k)
                if isinstance(v, int):
                    return v
            return None

        async def _vm_set(fields: Dict[str, int]) -> bool:
            try:
                await self.client.call("vm.set", {"id": vm_id, **fields})
                return True
            except Exception:
                return False

        any_success = False

        # Fetch current limits
        vm = await _get_vm()
        cur_dyn_min = _get_int(vm, "memoryDynamicMin", "memoryMin")
        cur_dyn_max = _get_int(vm, "memoryDynamicMax", "memoryMax")
        cur_stat_min = _get_int(vm, "memoryStaticMin")
        cur_stat_max = _get_int(vm, "memoryStaticMax")

        # 1) Lower staticMin first (safe: staticMin <= dynamicMin)
        if cur_stat_min is None or cur_stat_min > target:
            if await _vm_set({"memoryStaticMin": target}):
                any_success = True

        # 2) Raise staticMax if needed to accommodate dynamicMax/target
        smx_target = max(filter(None, [target, cur_dyn_max or 0]))  # max of target and current dyn max
        vm = await _get_vm()
        cur_stat_max = _get_int(vm, "memoryStaticMax") or cur_stat_max
        if cur_stat_max is None or cur_stat_max < smx_target:
            if await _vm_set({"memoryStaticMax": smx_target}):
                any_success = True

        # 3) Set dynamic min/max together; fallback to "memory"
        if await _vm_set({"memoryDynamicMin": target, "memoryDynamicMax": target}):
            any_success = True
        else:
            if await _vm_set({"memory": target}):
                any_success = True

        # 4) Tighten staticMax to target if above
        vm = await _get_vm()
        cur_stat_max = _get_int(vm, "memoryStaticMax") or cur_stat_max
        if cur_stat_max is None or cur_stat_max != target:
            if await _vm_set({"memoryStaticMax": target}):
                any_success = True

        # 5) Ensure staticMin equals target (final touch)
        if await _vm_set({"memoryStaticMin": target}):
            any_success = True

        print("🧠 Memory adjusted." if any_success else "⚠ Skipped memory tuning due to constraints.")

    async def _summary_and_deploy(
        self,
        tmpl: Dict[str, Any],
        template_id: str,
        vm_name: str,
        vm_desc: str,
        vcpus: int,
        ram_gib: int,
        memory_bytes: int,
        vifs: List[Dict[str, str]],
        user_cfg_name: str,
        cloud_config_text: str,
        net_cfg: Optional[Dict[str, Any]],
    ) -> None:

        # Summary
        print("\n📋 DEPLOYMENT SUMMARY")
        print(f"Template: {tmpl.get('name_label')} ({template_id})")
        print(f"VM Name: {vm_name}")
        print(f"Description: {vm_desc}")
        print(f"vCPUs: {vcpus}")
        print(f"RAM: {ram_gib} GiB")
        print("VIFs:")
        for v in vifs:
            net_id = v.get("network")
            net_label = self.inventory.get_network_label(net_id)
            print(f"  - Network: {net_label or '(unknown)'}  (ID: {net_id or 'missing'})  MAC: {v.get('mac', '(auto)')}")
        print(f"User Config: {user_cfg_name}")
        print(f"Network Config: {net_cfg['name'] if net_cfg else '(none)'}")
        proceed = confirm("Proceed with deployment?")
        if not proceed:
            print("🚫 Deployment cancelled.")
            return

        # Build vm.create params (disks come from template)
        params: Dict[str, Any] = {
            "name_label": vm_name,
            "name_description": vm_desc,
            "template": template_id,
            "CPUs": vcpus,
            "coresPerSocket": 1,
            "VIFs": vifs,
            "cloudConfig": cloud_config_text,
        }
        if net_cfg:
            params["networkConfig"] = net_cfg["template"]

        # Create VM (avoid sending memory limits here to prevent constraint violations)
        print("\n🚀 Creating VM...")
        result = await self.client.call("vm.create", params)
        new_vm_id = self._extract_id(result)
        print(f"✅ VM created: {vm_name} (ID: {new_vm_id})")

        # Adjust memory after creation, before starting (best-effort)
        await self._set_vm_memory(new_vm_id, memory_bytes)

        # Auto-start the VM (no prompt)
        try:
            await self.client.call("vm.start", {"id": new_vm_id})
            print("▶ VM started.")
        except Exception as e:
            print(f"⚠ VM created but failed to auto-start: {e}")

if __name__ == "__main__":
    async def standalone_main():
        async with XOClient(XO_HOST, XO_USER, KEYRING_SERVICE) as client:
            inventory = Inventory(client)
            repo = ConfigRepo(client)
            deployer = VMDeployer(client, inventory, repo)
            await deployer.run()

    try:
        asyncio.run(standalone_main())
    except KeyboardInterrupt:
        print("\nCancelled.")