FROM node:lts-slim

ENV DEBIAN_FRONTEND=noninteractive \
    PIP_NO_CACHE_DIR=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    TF_PLUGIN_CACHE_DIR=/opt/tf-cache

# Copy your script and tf-mirror config into the image
COPY scripts/01-install-base.sh /tmp/install-all.sh
COPY tf-mirror/ /tmp/tf-mirror/

# Non-root user setup first
RUN useradd -m app

# Run your install script (this will also use tf-mirror inside it)
RUN bash /tmp/install-all.sh && \
    rm -rf /tmp/install-all.sh /tmp/tf-mirror

# Set up workspace and permissions - full access as requested
RUN mkdir -p /workspace && \
    chown -R app:app /opt/tf-cache /workspace && \
    chmod -R 755 /opt/tf-cache /workspace
USER app
WORKDIR /workspace

CMD ["/bin/bash"]
