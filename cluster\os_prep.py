#!/usr/bin/env python3
"""
OS prep controller (Ubuntu 20.04/22.04). Uses Makefile for installs/configs.
Run with --mode=kubernetes or --mode=eks-anywhere. Must be run as root.
"""
import os
import subprocess
import json
import sys
import argparse
import time

class Controller:
    """Base controller with common utilities"""
    def __init__(self):
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        self.start_time = time.time()

    def log(self, msg, level="info"):
        """Enhanced logging with timestamps and colors"""
        elapsed = time.time() - self.start_time
        colors = {
            "info": "\033[94m",    # blue
            "warn": "\033[93m",    # yellow
            "error": "\033[91m",   # red
            "success": "\033[92m",  # green
            "debug": "\033[95m"     # purple
        }
        print(f"{colors.get(level)}[{level:^6}]\033[0m [{elapsed:6.1f}s] {msg}")

    def warn(self, msg):
        self.log(msg, "warn")

    def success(self, msg):
        self.log(msg, "success")

    def run_cmd(self, cmd, raise_on_error=True, silent=False):
        self.log(f"$ {' '.join(cmd)}", "debug")
        try:
            start = time.time()
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            duration = time.time() - start
            
            if result.stdout and not silent:
                for line in result.stdout.strip().split('\n'):
                    if line:
                        self.log(f"stdout: {line}", "debug")
            
            self.log(f"Command completed in {duration:.1f}s", "debug")
            return result.stdout.strip() if silent else None
        except subprocess.CalledProcessError as e:
            self.log(f"Command failed: {' '.join(e.cmd)}", "error")
            if e.stderr:
                for line in e.stderr.strip().split('\n'):
                    if line:
                        self.log(f"stderr: {line}", "error")
            if raise_on_error:
                sys.exit(1)
            return None

class PackageManager(Controller):
    """Handles package installation and validation"""
    def __init__(self, mode="kubernetes"):
        super().__init__()
        self.mode = mode
        self.versions = {}
        # Add make to the beginning of packages list
        self.packages = [
            ("make", ["make", "--version"]),
            ("bash", ["bash", "--version"]),
            ("containerd", ["containerd", "--version"]),
            ("git", ["git", "--version"]),
            ("curl", ["curl", "--version"]),
            ("wget", ["wget", "--version"]),
            ("jq", ["jq", "--version"]),
            ("python3", ["python3", "--version"]),
            ("pip3", ["pip3", "--version"]),
            ("docker", ["docker", "--version"]),
        ]
        if self.mode == "eks-anywhere":
            self.packages.extend([
                ("helm", ["helm", "version"]),
                ("eksctl", ["eksctl", "version"]),
                ("eksctl-anywhere", ["eksctl-anywhere", "version"]),
            ])
        self.packages.extend([
            ("kubectl", ["kubectl", "version", "--client"]),
            ("yq", ["yq", "--version"]),
            ("nfs-common", ["dpkg-query", "--showformat=${Version}", "--show", "nfs-common"]),
            ("xfsprogs", ["dpkg-query", "--showformat=${Version}", "--show", "xfsprogs"]),
            ("e2fsprogs", ["dpkg-query", "--showformat=${Version}", "--show", "e2fsprogs"]),
            ("parted", ["parted", "--version"]),
            ("xe-guest-utilities", ["dpkg-query", "--showformat=${Version}", "--show", "xe-guest-utilities"]),
            ("cloud-initramfs-growroot", ["dpkg-query", "--showformat=${Version}", "--show", "cloud-initramfs-growroot"]),
            ("nfs-kernel-server", ["rpcinfo", "-p"]),
        ])

    def check_package(self, name, check_cmd):
        try:
            res = subprocess.run(check_cmd, capture_output=True, text=True)
            if res.returncode == 0:
                out = (res.stdout or "").strip()
                if not out and res.stderr:
                    out = res.stderr.strip()
                if out:
                    self.versions[name] = out.splitlines()[0]
                    return True
            return False
        except FileNotFoundError:
            return False

    def install_prerequisites(self):
        # Check if make is installed
        if not self.check_package("make", ["make", "--version"]):
            self.log("Installing make...")
            try:
                self.run_cmd(["apt-get", "update"], raise_on_error=True)
                self.run_cmd(["apt-get", "install", "-y", "make"], raise_on_error=True)
            except Exception as e:
                self.warn(f"Failed to install make: {str(e)}")
                sys.exit(1)
        
        self.run_cmd(["make", f"MODE={self.mode}", "prerequisites"], raise_on_error=True)

    def validate_installations(self):
        self.log("Starting package validation...", "info")
        total = len(self.packages)
        success = 0
        
        print("\nPackage Status:")
        print("─" * 50)
        
        for i, (name, check_cmd) in enumerate(self.packages, 1):
            status = "✓" if self.check_package(name, check_cmd) else "✗"
            version = self.versions.get(name, "<missing>")
            print(f"\r[{i:2d}/{total:2d}] {status} {name:<20} {version}")
            if status == "✓":
                success += 1
        
        print("─" * 50)
        self.log(f"Validated {success}/{total} packages", "success")

class SystemManager(Controller):
    """Handles system configurations"""
    def __init__(self):
        super().__init__()
        self.cluster_name = "dev"
        self.host_ip = self._get_host_ip()

    def _get_host_ip(self):
        try:
            result = subprocess.run(["hostname", "-I"], capture_output=True, text=True, check=True)
            # Get first IP from DHCP assignment
            ip = result.stdout.strip().split()[0]
            self.log(f"Detected DHCP IP: {ip}")
            return ip
        except Exception:
            self.warn("Could not detect IP via DHCP, using fallback")
            return "127.0.0.1"

    def configure_cloud_init(self):
        self.run_cmd(["make", "cloud-init"], raise_on_error=False)

    def disable_swap(self):
        self.run_cmd(["make", "disable-swap"], raise_on_error=False)

    def setup_nfs_storage(self, disk_device="/dev/xvdb"):
        self.run_cmd(["make", f"DISK={disk_device}", "nfs-storage"], raise_on_error=False)

    def cleanup_users(self):
        self.log("Cleaning up user accounts...")
        keep_users = {
            "root", "daemon", "bin", "sys", "sync", "games", "man", "lp", "mail", "news", "uucp", "proxy", "www-data",
            "backup", "list", "irc", "gnats", "nobody", "systemd-timesync", "systemd-network", "systemd-resolve",
            "messagebus", "syslog", "landscape", "pollinate", "sshd", "systemd-coredump", "lxd", "dnsmasq", "usbmux",
            "rtkit", "cups-pk-helper", "geoclue", "pulse", "gdm", "hplip", "avahi", "colord", "speech-dispatcher",
            "kernoops", "whoopsie", "avahi-autoipd", "uuidd", "lightdm", "systemd-oom", "fwupd-refresh", "tcpdump",
            "_apt", "tss", "admin", "docker", "containerd", "xe-linux-distribution", "cloud-init", "dhcpcd", 
            "polkitd", "_rpc", "statd"  # Add users that are currently in use
        }
        result = self.run_cmd(["getent", "passwd"], silent=True)
        if not result:
            self.warn("Could not enumerate users")
            return

        users_to_remove = []
        for line in result.split("\n"):
            if ":" in line:
                parts = line.split(":")
                username = parts[0]
                try:
                    uid = int(parts[2])
                except Exception:
                    continue
                if username not in keep_users and (uid >= 1000 or (uid >= 100 and username not in keep_users)):
                    users_to_remove.append(username)

        removed_count = 0
        for username in users_to_remove:
            self.log(f"Attempting to remove user: {username}")
            # Check if user is currently logged in or has active processes
            check_result = self.run_cmd(["pgrep", "-u", username], raise_on_error=False, silent=True)
            if check_result:
                self.warn(f"User {username} has active processes, skipping removal")
                continue
            
            # Try to remove user
            if self.run_cmd(["userdel", "-r", username], raise_on_error=False) is None:
                self.warn(f"Failed to remove user {username} - may be in use")
            else:
                removed_count += 1
                
        # Clean up home directories more safely
        self.run_cmd(["find", "/home", "-maxdepth", "1", "-type", "d", "-empty", "-delete"], raise_on_error=False)
        self.success(f"User cleanup completed - removed {removed_count} users")

    def generate_report(self, versions):
        self.log("Generating installation report...")
        print("| Package          | Version |")
        print("|------------------|---------|")
        for pkg, version in sorted(versions.items()):
            print(f"| {pkg.ljust(16)} | {version} |")
        checkpoint = {
            "k8s_configured": True,
            "nfs_configured": True,
            "ssh_configured": True,
            "versions": versions,
            "nfs_details": {
                "host_ip": self.host_ip,
                "export_path": f"/srv/eks/{self.cluster_name}",
                "storage_class": "dev-nfs",
            },
        }
        with open("os_prep_status.json", "w") as f:
            json.dump(checkpoint, f, indent=2)
        self.success("Checkpoint written: os_prep_status.json")

if __name__ == "__main__":
    if os.geteuid() != 0:
        print("❌ Run as root: sudo python3 os_prep.py [--mode=kubernetes|eks-anywhere]")
        sys.exit(1)

    parser = argparse.ArgumentParser(description="OS prep for Kubernetes or EKS-Anywhere")
    parser.add_argument("--mode", choices=["kubernetes", "eks-anywhere"], default="kubernetes")
    args = parser.parse_args()

    pkg_mgr = PackageManager(mode=args.mode)
    sys_mgr = SystemManager()

    pkg_mgr.install_prerequisites()
    sys_mgr.configure_cloud_init()
    sys_mgr.disable_swap()
    sys_mgr.setup_nfs_storage()
    pkg_mgr.validate_installations()
    sys_mgr.cleanup_users()
    sys_mgr.generate_report(pkg_mgr.versions)
    sys_mgr.success("OS prep complete. Restarting...")
    sys_mgr.run_cmd(["shutdown", "-r", "now"])