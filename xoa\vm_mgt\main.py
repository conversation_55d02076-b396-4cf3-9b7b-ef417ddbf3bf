# main.py
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
from shared import *
from deploy import VMDeployer
from delete import delete_vms

async def main():
    async with XOClient(XO_HOST, XO_USER, KEYRING_SERVICE) as client:
        inventory = Inventory(client)
        repo = ConfigRepo(client)
        await inventory.refresh()
        action = term_pick(["Deploy", "Delete"], lambda a: a, "Select Action")
        if not action:
            print("Cancelled.")
            return
        if action == "Deploy":
            deployer = VMDeployer(client, inventory, repo)
            await deployer.run()
        elif action == "Delete":
            await delete_vms(client, inventory)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nCancelled.")