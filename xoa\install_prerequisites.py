#!/usr/bin/env python3
"""
EKS-Anywhere admin-VM installer (Ubuntu 20.04/22.04).
- Ensures prerequisites: Docker ≥20, python3-pip, git, yq, curl, wget, nfs-common, xfsprogs, e2fsprogs, parted, crictl, kubectl.
- Configures K8s requirements: swap disabled, kernel modules, sysctl, time sync.
- Uses simple-docker-cluster.yaml for configuration.
"""
import os, yaml, sys, json, shutil, requests, subprocess, click

def run_cmd(cmd, **kwargs):
    try:
        return subprocess.run(cmd, check=True, capture_output=True, text=True, **kwargs)
    except subprocess.CalledProcessError as e:
        print(f"\033[91m[error]\033[0m Command failed: {' '.join(e.cmd)}\n{e.stderr}")
        raise

def log(msg): print(f"\033[94m[info]\033[0m {msg}")
def warn(msg): print(f"\033[93m[warn]\033[0m {msg}")
def success(msg): print(f"\033[92m[✓]\033[0m {msg}")

def install_packages():
    """Install required packages."""
    packages = [
        ('docker', ['docker.io', '--version'], ['sudo', 'apt-get', 'install', '-y', 'docker.io'], ['sudo', 'systemctl', 'enable', '--now', 'docker'], ['sudo', 'usermod', '-aG', 'docker', os.environ['USER']]),
        ('pip3', ['pip3', '--version'], ['sudo', 'apt-get', 'install', '-y', 'python3-pip'], None, None),
        ('git', ['git', '--version'], ['sudo', 'apt-get', 'install', '-y', 'git'], None, None),
        ('yq', ['yq', '--version'], None, None, None, lambda: install_yq()),
        ('curl', ['curl', '--version'], ['sudo', 'apt-get', 'install', '-y', 'curl'], None, None),
        ('wget', ['wget', '--version'], ['sudo', 'apt-get', 'install', '-y', 'wget'], None, None),
        ('nfs-common', ['rpcinfo', '-V'], ['sudo', 'apt-get', 'install', '-y', 'nfs-common'], None, None),
        ('xfsprogs', ['mkfs.xfs', '--version'], ['sudo', 'apt-get', 'install', '-y', 'xfsprogs'], None, None),
        ('e2fsprogs', ['mkfs.ext4', '--version'], ['sudo', 'apt-get', 'install', '-y', 'e2fsprogs'], None, None),
        ('parted', ['parted', '--version'], ['sudo', 'apt-get', 'install', '-y', 'parted'], None, None),
        ('kubectl', ['kubectl', 'version', '--client'], None, None, None, lambda: install_kubectl()),
        ('crictl', ['crictl', '--version'], None, None, None, lambda: install_crictl())
    ]
    log("Starting package installation...")
    run_cmd(['sudo', 'apt-get', 'update', '-qq'])
    versions = {}
    for name, check_cmd, install_cmd, service_cmd, group_cmd, custom_install in packages:
        if shutil.which(name):
            warn(f"{name} already installed")
            versions[name] = run_cmd(check_cmd).stdout.strip().splitlines()[0]
            continue
        log(f"Installing {name}...")
        if custom_install:
            custom_install()
        else:
            run_cmd(install_cmd)
            if service_cmd:
                run_cmd(service_cmd)
            if group_cmd:
                run_cmd(group_cmd)
        versions[name] = run_cmd(check_cmd).stdout.strip().splitlines()[0]
        success(f"{name} installed")
    success("All prerequisites installed")
    print("\n\033[92m[✓] Installed versions:\033[0m")
    for name, version in versions.items():
        print(f"- {name}: {version}")
    return versions

def install_yq():
    r = requests.get('https://github.com/mikefarah/yq/releases/latest/download/yq_linux_amd64')
    with open('/tmp/yq', 'wb') as f:
        f.write(r.content)
    os.chmod('/tmp/yq', 0o755)
    run_cmd(['sudo', 'mv', '/tmp/yq', '/usr/local/bin/yq'])

def install_kubectl():
    r = requests.get('https://dl.k8s.io/release/stable.txt')
    version = r.text.strip()
    r = requests.get(f'https://dl.k8s.io/release/{version}/bin/linux/amd64/kubectl')
    with open('/tmp/kubectl', 'wb') as f:
        f.write(r.content)
    os.chmod('/tmp/kubectl', 0o755)
    run_cmd(['sudo', 'mv', '/tmp/kubectl', '/usr/local/bin/kubectl'])

def install_crictl():
    r = requests.get('https://api.github.com/repos/kubernetes-sigs/cri-tools/releases/latest')
    version = r.json()['tag_name']
    r = requests.get(f'https://github.com/kubernetes-sigs/cri-tools/releases/download/{version}/crictl-{version}-linux-amd64.tar.gz')
    with tarfile.open(fileobj=io.BytesIO(r.content)) as tar:
        tar.extract('crictl', '/tmp')
    os.chmod('/tmp/crictl', 0o755)
    run_cmd(['sudo', 'mv', '/tmp/crictl', '/usr/local/bin/crictl'])

def configure_k8s_requirements():
    """Configure Kubernetes-specific requirements."""
    log("Configuring Kubernetes requirements...")
    # Disable swap
    run_cmd(['sudo', 'swapoff', '-a'])
    run_cmd(['sudo', 'sed', '-i', '/swap/d', '/etc/fstab'])
    # Load kernel modules
    with open('/etc/modules-load.d/k8s.conf', 'w') as f:
        f.write('overlay\nbr_netfilter\n')
    run_cmd(['sudo', 'modprobe', 'overlay'])
    run_cmd(['sudo', 'modprobe', 'br_netfilter'])
    # Configure sysctl
    sysctl_conf = """
net.ipv4.ip_forward = 1
net.bridge.bridge-nf-call-iptables = 1
net.bridge.bridge-nf-call-ip6tables = 1
"""
    with open('/tmp/k8s-sysctl.conf', 'w') as f:
        f.write(sysctl_conf)
    run_cmd(['sudo', 'mv', '/tmp/k8s-sysctl.conf', '/etc/sysctl.d/k8s.conf'])
    run_cmd(['sudo', 'sysctl', '--system'])
    # Enable time sync
    run_cmd(['sudo', 'systemctl', 'enable', '--now', 'systemd-timesyncd'])
    success("Kubernetes requirements configured")

class ConfigReader:
    """Reads and parses YAML configuration."""
    def __init__(self, config_file="simple-docker-cluster.yaml"):
        self.config_file = config_file
        self.config = self.load_config()

    def load_config(self):
        if not os.path.exists(self.config_file):
            print(f"\033[93m[warn]\033[0m {self.config_file} not found")
            sys.exit(1)
        with open(self.config_file, 'r') as f:
            config = list(yaml.safe_load_all(f))
        success(f"Loaded {self.config_file}")
        return config

    def get_docker_datacenter_config(self):
        for doc in self.config or []:
            if doc.get('kind') == 'DockerDatacenterConfig' and doc.get('metadata', {}).get('name') == 'docker-dc':
                return doc.get('spec', {})
        return {}

class CheckpointManager:
    """Manages installation checkpoints."""
    def __init__(self, checkpoint_file=".eks_install_status.json"):
        self.checkpoint_file = checkpoint_file
        self.status = self._load_status()

    def _load_status(self):
        return json.load(open(self.checkpoint_file, 'r')) if os.path.exists(self.checkpoint_file) else {
            'packages_installed': False, 'docker_configured': False, 'k8s_configured': False, 'cluster_created': False, 'versions': {}
        }

    def save(self):
        json.dump(self.status, open(self.checkpoint_file, 'w'), indent=2)

    def get_status(self, key):
        return self.status.get(key, False)

    def set_status(self, key, value):
        self.status[key] = value
        self.save()

    def update_versions(self, versions):
        self.status['versions'] = versions
        self.save()

class TemplateManager:
    """Manages VM template cleanup operations."""
    def install_template_packages(self):
        """Install packages required for VM template."""
        packages = ["docker.io", "cloud-init", "cloud-initramfs-growroot", "curl", "git", "nfs-common", "xfsprogs", "e2fsprogs", "parted"]
        log("Installing template packages...")
        run_cmd(['sudo', 'apt-get', 'update', '-qq'])
        run_cmd(['sudo', 'apt-get', 'install', '-y'] + packages)
        run_cmd(['sudo', 'systemctl', 'enable', '--now', 'docker'])
        success("Template packages installed")

    def clean_template(self):
        """Clean VM for template creation."""
        log("Cleaning cloud-init state, users, and SSH keys...")
        for user in ["builder", "tempbuilder"]:
            run_cmd(['sudo', 'userdel', '-r', user], check=False)
        for cmd in [
            ['sudo', 'cloud-init', 'clean', '--logs', '--seed'],
            ['sudo', 'rm', '-rf', '/var/lib/cloud/instances'],
            ['sudo', 'rm', '-f', '/etc/cloud/cloud.cfg.d/*installer*'],
            ['sudo', 'rm', '-f', '/etc/ssh/ssh_host_*'],
            ['sudo', 'truncate', '-s', '0', '/etc/machine-id'],
            ['sudo', 'rm', '-f', '/etc/netplan/*.yaml'],
            ['sudo', 'apt-get', 'clean']
        ]:
            run_cmd(cmd, check=False)
        success("Template cleaned and ready")
        print("🔒 No users or keys remain.")

class InstallerOrchestrator:
    """Coordinates installation and cluster creation."""
    def __init__(self, config_file):
        self.config_reader = ConfigReader(config_file)
        self.checkpoint = CheckpointManager()
        self.template_manager = TemplateManager()

    def check_docker_access(self):
        try:
            run_cmd(['docker', 'version'])
            success("Docker access verified")
        except Exception as e:
            if "permission denied" in str(e).lower():
                print("\033[93m[warn]\033[0m Docker permission denied, fixing...")
                run_cmd(['sudo', 'groupadd', 'docker'], check=False)
                run_cmd(['sudo', 'usermod', '-aG', 'docker', os.environ['USER']])
                success("Docker permissions fixed. Log out and back in, then rerun.")
                sys.exit(0)
            print(f"\033[91m[error]\033[0m Docker error: {e}")

    def create_cluster(self):
        log("Creating dev cluster...")
        run_cmd(['eksctl', 'anywhere', 'create', 'cluster', '-f', 'simple-docker-cluster.yaml'])

    def check_existing_cluster(self):
        try:
            run_cmd(['kubectl', 'cluster-info'])
            success("Existing cluster found")
            return True
        except:
            return False

    def run_template_setup(self):
        log("Starting VM template setup...")
        self.template_manager.install_template_packages()
        configure_k8s_requirements()
        self.template_manager.clean_template()
        print("\n✅ Template setup complete. Shutting down in 5 seconds...")
        run_cmd(['sleep', '5'])
        run_cmd(['sudo', 'shutdown', 'now'])

    def run(self):
        log("Starting EKS Anywhere Installation...")
        if not self.checkpoint.get_status('packages_installed'):
            versions = install_packages()
            self.checkpoint.set_status('packages_installed', True)
            self.checkpoint.update_versions(versions)
        if not self.checkpoint.get_status('docker_configured'):
            self.check_docker_access()
            self.checkpoint.set_status('docker_configured', True)
        if not self.checkpoint.get_status('k8s_configured'):
            configure_k8s_requirements()
            self.checkpoint.set_status('k8s_configured', True)
        if not self.checkpoint.get_status('cluster_created'):
            if not self.check_existing_cluster():
                self.create_cluster()
            self.checkpoint.set_status('cluster_created', True)
        print("\n\033[92m✅ Installation and cluster setup complete.\033[0m")

@click.command()
@click.option('--config-file', default='simple-docker-cluster.yaml', help='Path to YAML configuration')
@click.option('--template-mode', is_flag=True, help='Run in template setup mode for VM preparation')
def main(config_file, template_mode):
    """Run EKS Anywhere installer or template setup."""
    try:
        orchestrator = InstallerOrchestrator(config_file)
        if template_mode:
            if os.geteuid() != 0:
                print("❌ Template mode requires root privileges. Please run with sudo.")
                sys.exit(1)
            orchestrator.run_template_setup()
        else:
            orchestrator.run()
    except Exception as e:
        print(f"\033[91m[error]\033[0m Installation failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()