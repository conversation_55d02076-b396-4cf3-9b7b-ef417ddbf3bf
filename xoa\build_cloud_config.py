#!/usr/bin/env python3
"""
Interactive cloud-init / network-config template manager for Xen Orchestra (XO).
Scans for YAML files (excluding simple-docker-cluster.yaml), allows selection, preview, and deployment,
and lets you delete remote configs. Network templates are now correctly routed to the Network Configs area.

Updates:
- Standard naming: custom_<underscored_name> (lowercase, spaces/dashes -> underscores).
- Skip deploy if a config with the same normalized name already exists (no overwrite).
- Auto-deploy after preview using default config name (no Enter required).
"""
import getpass
import json
import keyring
import ssl
import asyncio
import websockets
import logging
import re
from pathlib import Path
from rich.console import Console
from rich.prompt import Prompt
from rich.panel import Panel
from rich.table import Table
from rich.text import Text
import pandas as pd

# ── Xen Orchestra connection details ────────────────────────────────────────────
XO_URL = "https://*************"   # Keep scheme; websocket code derives wss:// from this
XO_USERNAME = "<EMAIL>"

# ── File discovery settings ────────────────────────────────────────────────────
SEARCH_DIRS = [Path("config_template"), Path("config_templates"), Path("templates"), Path(".")]
OUTPUT_DIR = Path("config_templates")
EXCLUDED_FILES = ["simple-docker-cluster.yaml"]

# ── Logging ────────────────────────────────────────────────────────────────────
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.FileHandler('cloud_config_deployment.log'), logging.StreamHandler()]
)
logger = logging.getLogger(__name__)
console = Console()

# ───────────────────────────────────────────────────────────────────────────────
# Name normalization helpers
# ───────────────────────────────────────────────────────────────────────────────

def _underscorize(s: str) -> str:
    """
    Convert a string to lowercase underscore form:
    - Replace spaces and dashes with underscores
    - Collapse multiple underscores
    - Keep alphanumerics and underscores
    """
    s = s.strip().lower()
    s = s.replace(' ', '_').replace('-', '_')
    # Remove any chars not alnum or underscore
    s = re.sub(r'[^a-z0-9_]+', '_', s)
    s = re.sub(r'_+', '_', s).strip('_')
    return s

def normalize_config_name(raw_name: str, default_stem: str) -> str:
    """
    Enforce naming convention: custom_<underscored_name>
    - If raw_name is empty, use default_stem.
    - Remove leading 'cloud-init' variants to avoid duplication.
    - Ensure a single 'custom_' prefix.
    """
    base = raw_name or default_stem
    base = _underscorize(base)

    # Strip common prefixes we don't want duplicated
    for bad_prefix in ('cloud_init_', 'cloudinit_', 'cloud_init', 'cloudinit', 'cloud-init_', 'cloud-init'):
        if base.startswith(bad_prefix):
            base = base[len(bad_prefix):].lstrip('_')

    # Ensure single custom_ prefix
    if base.startswith('custom_'):
        final = base
    else:
        final = f'custom_{base}'

    return final or 'custom_config'

# ───────────────────────────────────────────────────────────────────────────────
# Discovery & selection
# ───────────────────────────────────────────────────────────────────────────────

def find_yaml_files():
    """Find all YAML files in search directories, excluding specified files."""
    yaml_files = []
    for search_dir in SEARCH_DIRS:
        if search_dir.exists():
            dir_files = [
                f for pattern in ["*.yaml", "*.yml"]
                for f in search_dir.glob(pattern)
                if f.name not in EXCLUDED_FILES
            ]
            yaml_files.extend(dir_files)

    unique = sorted(set(yaml_files))
    console.print(f"\n[bold green]📊 Total YAML files found: {len(unique)}[/bold green]")
    return unique

def display_yaml_selection(yaml_files):
    """Display available YAML files for selection."""
    if not yaml_files:
        console.print(Panel("❌ [red]No YAML files found[/red]", title="Error"))
        return None

    table = Table(title="📄 Available YAML Templates")
    table.add_column("Index", style="cyan")
    table.add_column("File", style="green")
    table.add_column("Directory", style="blue")

    for i, file_path in enumerate(yaml_files, 1):
        table.add_row(str(i), file_path.name, str(file_path.parent))
    console.print(table)

    while True:
        choice = Prompt.ask(
            f"Select template [cyan](1-{len(yaml_files)})[/cyan], [yellow]'b'[/yellow] back, or [red]'q'[/red] to quit",
            default="b"
        ).strip().lower()
        if choice in ('q', 'quit'):
            return 'quit'
        if choice in ('b', 'back'):
            return 'back'
        try:
            index = int(choice) - 1
            if 0 <= index < len(yaml_files):
                return yaml_files[index]
            console.print(f"[red]❌ Please enter a number between 1 and {len(yaml_files)}[/red]")
        except ValueError:
            console.print("[red]❌ Please enter a valid number, 'b' for back, or 'q' to quit[/red]")

# ───────────────────────────────────────────────────────────────────────────────
# Type detection & preview
# ───────────────────────────────────────────────────────────────────────────────

def detect_template_type(template_path: Path) -> str:
    """
    Simple template type detection - mainly for naming/display purposes.
    All configs are deployed to cloudConfig regardless of type.
    """
    try:
        content = template_path.read_text()
        lower = content.lower().lstrip()

        # Check for network-config patterns (for naming only)
        if (lower.startswith('#network') or 
            'network:' in lower or 
            any(k in lower for k in ('ethernets:', 'vlans:', 'bonds:', 'bridges:', 'wifis:')) or
            any(tok in template_path.stem.lower() for tok in ('net', 'network', 'nic'))):
            logger.info(f"Detected network-config pattern: {template_path.name}")
            return 'network-config'
        
        logger.info(f"Detected cloud-config pattern: {template_path.name}")
        return 'cloud-config'

    except Exception as e:
        console.print(f"[red]❌ Error reading template: {e}[/red]")
        logger.error(f"Error detecting template type for {template_path.name}: {e}")
        return 'cloud-config'

def preview_template(template_path):
    """Show a preview of the selected template with detected type."""
    try:
        tmpl_type = detect_template_type(template_path)
        content = template_path.read_text()
        lines = content.split('\n')
        preview_text = Text()
        preview_text.append(f"Detected type: {tmpl_type}\n", style="bold green")

        for i, line in enumerate(lines[:10], 1):
            preview_text.append(f"{i:2d}: {line}\n", style="dim white")
        if len(lines) > 10:
            preview_text.append(f"... ({len(lines) - 10} more lines)", style="dim yellow")

        console.print(Panel(preview_text, title=f"📋 Template Preview: [cyan]{template_path.name}[/cyan]", border_style="blue"))
        return True  # Always proceed with deployment after preview
    except Exception as e:
        console.print(f"[red]❌ Error reading template: {e}[/red]")
        return False

# ───────────────────────────────────────────────────────────────────────────────
# Debug & summaries
# ───────────────────────────────────────────────────────────────────────────────

def print_debug_summary(debug_data):
    """Print debug information in a table format."""
    if debug_data:
        df = pd.DataFrame(debug_data)
        print("\n" + "-"*60 + "\n🔍 DEBUG INFORMATION\n" + "-"*60)
        print(df.to_string(index=False))
        print("-"*60)

def print_deployment_summary(success, template_path, config_name, cloud_config_id=None):
    """Print deployment summary (remote only, no local save)."""
    template_type = detect_template_type(template_path)
    summary_data = {
        'Operation': ['Authentication', 'Template Read', f'{template_type.title()} Deploy'],
        'Status': ['✓ Success', '✓ Success', '✓ Success' if success else '✗ Failed'],
        'Details': [
            'XO WebSocket connection established',
            f'Template loaded: {template_path.name}',
            f'Config "{config_name}" ID: {cloud_config_id}' if cloud_config_id else 'Deployment failed'
        ]
    }
    df = pd.DataFrame(summary_data)
    print("\n" + "="*80 + "\n🚀 DEPLOYMENT SUMMARY\n" + "="*80)
    print(df.to_string(index=False, justify='left'))
    print("="*80)

# ───────────────────────────────────────────────────────────────────────────────
# XO auth helpers
# ───────────────────────────────────────────────────────────────────────────────

async def authenticate_and_get_token(xo_password):
    """Authenticate with Xen Orchestra and get a token."""
    uri = f"wss://{XO_URL.split('://')[1]}/api/"
    ssl_context = ssl._create_unverified_context()

    try:
        async with websockets.connect(uri, ssl=ssl_context) as websocket:
            signin_payload = {
                "id": 0, "jsonrpc": "2.0", "method": "session.signIn",
                "params": {"email": XO_USERNAME, "password": xo_password}
            }
            await websocket.send(json.dumps(signin_payload))
            response = await websocket.recv()
            data = json.loads(response)

            if "result" not in data:
                logger.info("Sign in failed")
                return None, data.get('error')

            token_payload = {"id": 1, "jsonrpc": "2.0", "method": "token.create", "params": {}}
            await websocket.send(json.dumps(token_payload))
            response = await websocket.recv()
            data_token = json.loads(response)

            if "result" in data_token:
                logger.info("Authentication successful, token created")
                return data_token["result"], None
            else:
                logger.info("Token creation failed")
                return None, data_token.get('error')

    except Exception as e:
        logger.error(f"WebSocket connection failed: {e}")
        return None, {'message': str(e)}

def authenticate_xo():
    """Authenticate with Xen Orchestra using system keyring, with fallback to prompt."""
    use_saved = True
    while True:
        if use_saved:
            xo_password = keyring.get_password("xen_orchestra", XO_USERNAME)
            if not xo_password:
                use_saved = False
                continue
            logger.info("Using saved password from keyring")
        else:
            xo_password = getpass.getpass("Enter XO password: ")
            keyring.set_password("xen_orchestra", XO_USERNAME, xo_password)
            logger.info("Password saved to keyring")

        token, error = asyncio.run(authenticate_and_get_token(xo_password))
        if token:
            return {"authenticationToken": token}

        if error and error.get('code') == 3:
            if use_saved:
                keyring.delete_password("xen_orchestra", XO_USERNAME)
                logger.info("Deleted invalid saved password from keyring")
                use_saved = False
            else:
                logger.info("Invalid credentials, please try again")
        else:
            logger.info("Authentication failed due to other error")
            return None

# ───────────────────────────────────────────────────────────────────────────────
# Create / delete via WebSocket
# ───────────────────────────────────────────────────────────────────────────────

async def deploy_via_websocket(token, template_content, config_name, template_path):
    """Deploy template via WebSocket with proper template type detection."""
    uri = f"wss://{XO_URL.split('://')[1]}/api/"
    ssl_context = ssl._create_unverified_context()

    try:
        async with websockets.connect(uri, ssl=ssl_context) as websocket:
            signin_token_payload = {
                "id": 0, "jsonrpc": "2.0", "method": "session.signInWithToken",
                "params": {"token": token}
            }
            await websocket.send(json.dumps(signin_token_payload))
            response = await websocket.recv()
            data = json.loads(response)
            if "result" not in data:
                logger.info("Token sign-in failed during deployment")
                return False
            
            create_params = {"name": config_name, "template": template_content}

            # All configs go to cloudConfig - XO handles categorization
            template_type = detect_template_type(template_path)  # For logging only
            api_method = "cloudConfig.create"

            logger.info(f"Deploying '{config_name}' using {api_method}")

            create_payload = {"id": 1, "jsonrpc": "2.0", "method": api_method, "params": create_params}
            await websocket.send(json.dumps(create_payload))
            response = await websocket.recv()
            create_data = json.loads(response)

            if "result" in create_data:
                config_id = create_data["result"].get("id", "unknown")
                logger.info(f"Successfully created {template_type} '{config_name}' with ID: {config_id}")
                return create_data["result"]
            else:
                error_msg = create_data.get('error', {}).get('message', 'Unknown error')
                logger.error(f"Failed to create {template_type}: {error_msg}")
                return False

    except Exception as e:
        logger.error(f"WebSocket connection failed: {e}")
        return False

def deploy_to_xo(session, template_content, config_name, template_path):
    """Deploy the template to Xen Orchestra."""
    token = session["authenticationToken"]
    return asyncio.run(deploy_via_websocket(token, template_content, config_name, template_path))

async def list_remote_configs(token):
    """List all saved configs from Xen Orchestra."""
    uri = f"wss://{XO_URL.split('://')[1]}/api/"
    ssl_context = ssl._create_unverified_context()

    try:
        async with websockets.connect(uri, ssl=ssl_context) as websocket:
            # Sign in with token
            await websocket.send(json.dumps({
                "id": 0, "jsonrpc": "2.0", "method": "session.signInWithToken",
                "params": {"token": token}
            }))
            if "result" not in json.loads(await websocket.recv()):
                return []

            # Get ALL saved configs
            await websocket.send(json.dumps({
                "id": 1, "jsonrpc": "2.0", "method": "cloudConfig.getAll", "params": {}
            }))
            resp = json.loads(await websocket.recv())
            if "result" not in resp:
                return []

            configs = []
            for cfg in resp["result"]:
                # Typical fields: id, name, template
                cfg_id = cfg.get("id", "unknown")
                name = cfg.get("name", "unnamed")
                configs.append({"id": cfg_id, "name": name, "type": "cloud-config"})
            return configs

    except Exception as e:
        console.print(f"[red]❌ Error listing configs: {e}[/red]")
        return []

async def delete_remote_config(token, config_id, config_type):
    """Delete a config from Xen Orchestra."""
    uri = f"wss://{XO_URL.split('://')[1]}/api/"
    ssl_context = ssl._create_unverified_context()

    try:
        async with websockets.connect(uri, ssl=ssl_context) as websocket:
            # Sign in with token
            signin_token_payload = {"id": 0, "jsonrpc": "2.0", "method": "session.signInWithToken", "params": {"token": token}}
            await websocket.send(json.dumps(signin_token_payload))
            response = await websocket.recv()
            data = json.loads(response)
            if "result" not in data:
                return False
            
            # Always use cloudConfig.delete - XO stores both types under cloudConfig
            api_method = "cloudConfig.delete"
            delete_payload = {"id": 1, "jsonrpc": "2.0", "method": api_method, "params": {"id": config_id}}

            await websocket.send(json.dumps(delete_payload))
            response = await websocket.recv()
            delete_data = json.loads(response)
            return "result" in delete_data

    except Exception as e:
        console.print(f"[red]❌ Error deleting config: {e}[/red]")
        return False

# ───────────────────────────────────────────────────────────────────────────────
# Existence checks
# ───────────────────────────────────────────────────────────────────────────────

def config_exists(token: str, normalized_name: str) -> bool:
    """
    Return True if a remote config with the given normalized name already exists.
    Comparison is case-insensitive, but we normalize to our convention anyway.
    """
    existing = asyncio.run(list_remote_configs(token))
    names = { _underscorize(c['name']) for c in existing }
    return _underscorize(normalized_name) in names

# ───────────────────────────────────────────────────────────────────────────────
# Deletion UX
# ───────────────────────────────────────────────────────────────────────────────

def display_remote_configs_for_deletion(configs):
    """Display remote configs in a table for deletion selection."""
    if not configs:
        console.print(Panel("❌ [red]No remote configs found[/red]", title="Error"))
        return None

    table = Table(title="🗑️ Remote Configs Available for Deletion")
    table.add_column("Index", style="cyan")
    table.add_column("Name", style="green")
    table.add_column("Type", style="blue")
    table.add_column("ID", style="yellow")

    for i, config in enumerate(configs, 1):
        type_color = "cyan" if config['type'] == "cloud-config" else "magenta"
        table.add_row(str(i), config['name'], f"[{type_color}]{config['type']}[/{type_color}]", config['id'][:8] + "...")
    console.print(table)

    while True:
        choice = Prompt.ask(
            f"Select config(s) to delete [cyan](1-{len(configs)})[/cyan], [cyan]'a'[/cyan] for all, [cyan]'m'[/cyan] for multiple, [yellow]'b'[/yellow] back, or [red]'q'[/red] quit",
            default="b"
        ).strip().lower()

        if choice in ('q', 'quit'):
            return 'quit'
        if choice in ('b', 'back'):
            return 'back'
        if choice == 'a':
            return configs  # all
        if choice == 'm':
            return select_multiple_configs(configs)

        try:
            index = int(choice) - 1
            if 0 <= index < len(configs):
                return [configs[index]]
            else:
                console.print(f"[red]❌ Please enter a number between 1 and {len(configs)}[/red]")
        except ValueError:
            console.print("[red]❌ Please enter a valid number, 'a' for all, 'm' for multiple, 'b' for back, or 'q' to quit[/red]")

def select_multiple_configs(configs):
    """Allow user to select multiple configs for deletion."""
    console.print("\n[bold blue]Multiple Selection Mode[/bold blue]")
    console.print("Enter config numbers separated by commas (e.g., 1,3,5) or ranges (e.g., 1-3,5)")

    while True:
        selection = Prompt.ask("Select configs", default="back").strip().lower()

        if selection in ('back', 'b'):
            return 'back'
        if selection in ('quit', 'q'):
            return 'quit'

        try:
            selected_configs = []
            parts = selection.split(',')

            for part in parts:
                part = part.strip()
                if '-' in part:
                    start, end = map(int, part.split('-'))
                    for i in range(start, end + 1):
                        if 1 <= i <= len(configs):
                            selected_configs.append(configs[i - 1])
                else:
                    i = int(part)
                    if 1 <= i <= len(configs):
                        selected_configs.append(configs[i - 1])

            if selected_configs:
                console.print(f"\n[bold green]Selected {len(selected_configs)} config(s):[/bold green]")
                for config in selected_configs:
                    type_color = "cyan" if config['type'] == "cloud-config" else "magenta"
                    console.print(f"- [{type_color}]{config['name']}[/{type_color}] ({config['type']})")

                confirm = Prompt.ask("Proceed with these selections?", choices=["y", "n"], default="y")
                if confirm == "y":
                    return selected_configs
            else:
                console.print("[red]❌ No valid configs selected[/red]")

        except ValueError:
            console.print("[red]❌ Invalid format. Use numbers separated by commas or ranges (e.g., 1,3,5 or 1-3,5)[/red]")

def manage_remote_configs():
    """Manage (delete) remote configs in Xen Orchestra."""
    while True:
        console.print("\n🔐 [bold blue]Authenticating with Xen Orchestra...[/bold blue]")
        session = authenticate_xo()
        if not session:
            console.print("❌ [red]Cannot proceed without XO authentication[/red]")
            return

        console.print("📋 [bold blue]Fetching remote configs...[/bold blue]")
        token = session["authenticationToken"]
        configs = asyncio.run(list_remote_configs(token))

        if not configs:
            console.print("ℹ️ [yellow]No remote configs found[/yellow]")
            return

        selected_configs = display_remote_configs_for_deletion(configs)
        if selected_configs in ('quit', 'back'):
            return
        if not selected_configs:
            continue

        # Ensure list type
        if not isinstance(selected_configs, list):
            selected_configs = [selected_configs]

        console.print(f"\n[bold red]⚠️  You are about to delete {len(selected_configs)} config(s):[/bold red]")
        for config in selected_configs:
            type_color = "cyan" if config['type'] == "cloud-config" else "magenta"
            console.print(f"- [{type_color}]{config['name']}[/{type_color}] ({config['type']})")

        confirm = Prompt.ask(
            f"Are you sure you want to delete these {len(selected_configs)} config(s)?",
            choices=["y", "n"],
            default="n"
        )

        if confirm == "y":
            success_count = 0
            for config in selected_configs:
                console.print(f"🗑️ [bold red]Deleting '{config['name']}'...[/bold red]")
                success = asyncio.run(delete_remote_config(token, config['id'], config['type']))
                if success:
                    console.print(f"✅ [green]Successfully deleted '{config['name']}'[/green]")
                    success_count += 1
                else:
                    console.print(f"❌ [red]Failed to delete '{config['name']}'[/red]")

            console.print(f"\n📊 [bold blue]Deletion Summary: {success_count}/{len(selected_configs)} successful[/bold blue]")

            console.print("\n🔄 [bold blue]Refreshing remote configs...[/bold blue]")
            updated_configs = asyncio.run(list_remote_configs(token))
            if updated_configs:
                console.print(f"\n📋 [bold green]Updated Remote Configs ({len(updated_configs)} remaining):[/bold green]")
                table = Table(title="📋 Current Remote Configs")
                table.add_column("Name", style="green")
                table.add_column("Type", style="blue")
                for config in updated_configs:
                    type_color = "cyan" if config['type'] == "cloud-config" else "magenta"
                    table.add_row(config['name'], f"[{type_color}]{config['type']}[/{type_color}]")
                console.print(table)
            else:
                console.print("✨ [green]No remote configs remaining[/green]")

            continue_manage = Prompt.ask("Continue managing configs?", choices=["y", "n"], default="n")
            if continue_manage == "n":
                break
        else:
            console.print("👋 [yellow]Deletion cancelled[/yellow]")
            break

# ───────────────────────────────────────────────────────────────────────────────
# Deploy flow
# ───────────────────────────────────────────────────────────────────────────────

def deploy_template():
    """Deploy a new template to Xen Orchestra (remote only, no local save)."""
    console.print("\n🔍 [bold blue]Scanning for YAML templates...[/bold blue]")
    yaml_files = find_yaml_files()
    selected_template = display_yaml_selection(yaml_files)
    if selected_template in ('quit', 'back') or not selected_template:
        return selected_template
    
    if not preview_template(selected_template):
        console.print("❌ [red]Deployment cancelled[/red]")
        return
    
    # Derive a clean default stem from filename, then normalize as custom_<underscored>
    clean_stem = selected_template.stem
    # Strip a leading 'cloud-init-' if present to avoid duplication
    if clean_stem.startswith('cloud-init-'):
        clean_stem = clean_stem[11:]

    # Use default name directly without prompting
    normalized_name = normalize_config_name("", default_stem=clean_stem)
    console.print(f"[bold]Using normalized name:[/bold] [green]{normalized_name}[/green]")

    console.print("\n🔐 [bold blue]Authenticating with Xen Orchestra...[/bold blue]")
    session = authenticate_xo()
    if not session:
        console.print("❌ [red]Cannot proceed without XO authentication[/red]")
        return

    token = session["authenticationToken"]

    # Skip if already exists
    if config_exists(token, normalized_name):
        console.print(f"⏭️  [yellow]Config '{normalized_name}' already exists. Skipping deployment.[/yellow]")
        logger.info(f"Skipped deployment; '{normalized_name}' already exists remotely.")
        return

    try:
        template_content = selected_template.read_text()
    except Exception as e:
        console.print(f"❌ [red]Error reading template: {e}[/red]")
        return

    console.print(f"\n🚀 [bold green]Deploying '{normalized_name}' to Xen Orchestra (remote only)...[/bold green]")
    deployment_result = deploy_to_xo(session, template_content, normalized_name, selected_template)

    if deployment_result:
        cloud_config_id = deployment_result.get('id') if isinstance(deployment_result, dict) else None
        logger.info(f"Successfully deployed '{normalized_name}' with ID: {cloud_config_id}")
        print_deployment_summary(True, selected_template, normalized_name, cloud_config_id)
    else:
        logger.error(f"Failed to deploy '{normalized_name}'")
        print_deployment_summary(False, selected_template, normalized_name)

# ───────────────────────────────────────────────────────────────────────────────
# Main menu
# ───────────────────────────────────────────────────────────────────────────────

def main():
    console.print("🔍 [bold blue]Cloud-init Template Manager[/bold blue]")
    while True:
        console.print("\n📋 [bold cyan]Choose an option:[/bold cyan]")
        console.print("1. Deploy new template")
        console.print("2. Delete remote config")
        console.print("3. Exit")

        choice = Prompt.ask("Select option", choices=["1", "2", "3"], default="1")
        if choice == "1":
            result = deploy_template()
            if result == 'quit':
                break
        elif choice == "2":
            manage_remote_configs()
        elif choice == "3":
            console.print("👋 [yellow]Goodbye![/yellow]")
            break

if __name__ == "__main__":
    main()