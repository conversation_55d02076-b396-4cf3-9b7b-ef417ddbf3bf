# shared.py
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import json
import ssl
import websockets
import getpass
import keyring
import re
from typing import Any, Dict, List, Optional, Tuple
from pathlib import Path
from datetime import datetime
# Try optional Questionary for multi-select deletion
try:
    import questionary  # type: ignore
    HAS_QUESTIONARY = True
except Exception:
    questionary = None  # type: ignore
    HAS_QUESTIONARY = False

# ====================== SETTINGS ======================
XO_HOST = "*************"            # XO host (no scheme)
XO_USER = "<EMAIL>"
KEYRING_SERVICE = "xen_orchestra"

# Filter templates by prefix; set to "" for all
NAME_PREFIX = "deploy-"  # <--- Changed this line

# Deployment defaults moved to top
DEFAULT_VCPUS = 4
DEFAULT_RAM_GIB = 4

# Where we persist the last-used config
CHECKPOINT_PATH = Path("vm_deploy_checkpoint.json")
# Default local Jinja2 cloud-config template path (relative to this script)
DEFAULT_J2_PATH = (Path(__file__).resolve().parent / "config_template" / "cloud-init-admin-host.yaml.j2")
# Always use this fixed J2 template path (no prompt unless missing)
FIXED_J2_PATH = Path("/mnt/c/Users/<USER>/OneDrive/Documents/GithubWerk-AWS-Samples/xen_orchestra/xoa/config_template/cloud-init-admin-host.yaml.j2")

# Never delete these VM IDs
PROTECTED_VM_IDS = {
    "d52f6352-2d6d-874c-1010-9da4785e169b",  # XOA
    "6e520543-d0a2-66f7-23c4-a6a7be9b7085",  # lee
    "04c0fdc8-6636-9d33-11bd-dffc3338a2ce",  # ubuntu-noble-24.04-cloudimg-20250805
}

def confirm(prompt: str) -> bool:
    """Safely ask for confirmation, requiring full input to prevent accidental key press issues."""
    while True:
        response = input(f"{prompt} [y/n]: ").strip().lower()
        if response in ("y", "yes"):
            return True
        elif response in ("n", "no"):
            return False
        print("Please enter 'y' or 'n'.")

# ====================== UTILITIES ======================
def ask(prompt: str, default: Optional[str] = None) -> str:
    # Use full input() to get the entire line, not just first keypress
    s = input(f"{prompt}{f' [{default}]' if default is not None else ''}: ").strip()
    # Only return default if the user pressed Enter without typing anything
    return s if s else (default or "")

def ask_int(prompt: str, default: int) -> int:
    s = input(f"{prompt} [{default}]: ").strip()
    return int(s) if s else default

def bytes_from_gib(n: int) -> int:
    return int(n * (1024 ** 3))

def ask_path(prompt: str, default: Path) -> Path:
    """Ask for a filesystem path; accept Enter, 'y', or 'yes' to use the default."""
    s = input(f"{prompt} [{default}]: ").strip()
    if s == "" or s.lower() in ("y", "yes"):
        s = str(default)
    return Path(s).expanduser()

def term_pick(items: List[Any], label_fn, title: str, allow_skip: bool = False) -> Optional[Any]:
    print(f"\n{title}")
    if not items:
        print("  (none)")
        return None
    for i, it in enumerate(items, 1):
        print(f"  {i}. {label_fn(it)}")
    prompt = f"Select (1-{len(items)})"
    if allow_skip:
        prompt += " or Enter to skip"
    prompt += ", or 'q' to cancel: "
    while True:
        s = input(prompt).strip().lower()
        if s in ("q", "quit"):
            return None
        if allow_skip and s == "":
            return None
        try:
            idx = int(s)
            if 1 <= idx <= len(items):
                return items[idx - 1]
        except Exception:
            pass
        print("Invalid choice.")

def extract_j2_defaults(template_path: Path) -> Dict[str, Optional[str]]:
    try:
        content = template_path.read_text()
    except Exception:
        return {}
    defaults = {}
    for var in ['hostname', 'fqdn', 'username', 'password']:
        # Look for patterns like {{ var | default('value') }} anywhere in the content
        # Handle both single and double quotes, and optional whitespace
        # Build regex pattern to match {{ var | default('value') }}
        var_escaped = re.escape(var)
        # Pattern parts: {{ var | default('value') }}
        start = r"{{\s*"
        middle = r"\s*\|\s*default\(\s*"
        quotes = r"['\"]([^'\"]*)['\"]"
        end = r"\s*\)\s*}}"
        pattern = start + var_escaped + middle + quotes + end
        match = re.search(pattern, content)
        if match:
            defaults[var] = match.group(1)
    return defaults

def strip_prefix(s: str, prefix: str) -> str:
    """Return s without the leading prefix (case-insensitive) if present."""
    if not s or not prefix:
        return s
    return s[len(prefix):] if s.lower().startswith(prefix.lower()) else s

async def multi_pick(items: List[Any], label_fn, title: str) -> List[Any]:
    """Multi-select using Questionary (in a thread) when available; fallback to manual input."""
    if HAS_QUESTIONARY:
        def _ask_checkbox():
            choices = [questionary.Choice(title=label_fn(it), value=it) for it in items]  # type: ignore[attr-defined]
            return questionary.checkbox(title, choices=choices).ask()  # type: ignore[attr-defined]
        try:
            selected = await asyncio.to_thread(_ask_checkbox)
            return selected or []
        except Exception as e:
            print(f"⚠ Questionary unavailable here: {e}. Falling back to manual input.")

    # Fallback: comma-separated indexes
    print(f"\n{title}")
    if not items:
        print("  (none)")
        return []
    for i, it in enumerate(items, 1):
        print(f"  {i}. {label_fn(it)}")
    while True:
        s = input("Select indexes (e.g., 1,3,5) or 'q' to cancel: ").strip().lower()
        if s in ("q", "quit", ""):
            return []
        try:
            idxs = sorted({int(x) for x in re.split(r"[,\s]+", s) if x})
            picked = [items[i - 1] for i in idxs if 1 <= i <= len(items)]
            if picked:
                return picked
        except Exception:
            pass
        print("Invalid selection. Try again.")

# ====================== CLASS 1: CLIENT ======================
class XOClient:
    """Handles connection, auth, and JSON-RPC calls to Xen Orchestra."""
    def __init__(self, host: str, user: str, keyring_service: str):
        self.host = host
        self.user = user
        self.keyring_service = keyring_service
        self.ws: Optional[websockets.WebSocketClientProtocol] = None
        self.ssl_ctx = ssl._create_unverified_context()  # XO often uses self-signed certs
        self._next_id = 1  # RPC id counter

    async def __aenter__(self):
        await self.connect()
        await self.sign_in()
        return self

    async def __aexit__(self, exc_type, exc, tb):
        await self.close()

    async def connect(self):
        uri = f"wss://{self.host}/api/"
        self.ws = await websockets.connect(uri, ssl=self.ssl_ctx)

    async def close(self):
        if self.ws:
            await self.ws.close()
            self.ws = None

    async def call(self, method: str, params: Optional[Dict[str, Any]] = None) -> Any:
        """
        Robust JSON-RPC: assign a unique id, send, then read frames until we get
        the response with our id. Ignore push/event frames like {'method':'all',...}.
        """
        if not self.ws:
            raise RuntimeError("WebSocket not connected.")

        rpc_id = self._next_id
        self._next_id += 1

        payload = {"id": rpc_id, "jsonrpc": "2.0", "method": method, "params": params or {}}
        await self.ws.send(json.dumps(payload))

        while True:
            raw = await self.ws.recv()
            try:
                resp = json.loads(raw)
            except Exception:
                # Unknown frame, keep waiting
                continue

            # Event/push frames (e.g., task updates)
            if isinstance(resp, dict) and "method" in resp and resp.get("method") == "all":
                continue

            # We only care about the frame matching our id
            if isinstance(resp, dict) and resp.get("id") == rpc_id:
                if "error" in resp:
                    raise RuntimeError(f"{method} failed: {resp['error']}")
                if "result" in resp:
                    return resp["result"]
                raise RuntimeError(f"{method} unexpected response: {resp}")

            # Frames for other in-flight calls: ignore
            continue

    async def sign_in(self):
        """
        Sign in using keyring-stored password if available.
        If XO returns code 3 (auth failed), clear keyring and re-prompt.
        """
        pw = keyring.get_password(self.keyring_service, self.user)
        first_attempt = True
        while True:
            if not pw:
                pw = getpass.getpass(f"Password for {self.user}: ")
            try:
                await self.call("session.signIn", {"email": self.user, "password": pw})
                print(f"✅ Logged in as {self.user}")
                # Save to keyring if not already
                if first_attempt and keyring.get_password(self.keyring_service, self.user) is None:
                    keyring.set_password(self.keyring_service, self.user, pw)
                    print("🔑 Password saved to system keyring.")
                return
            except RuntimeError as e:
                msg = str(e)
                bad_creds = ("'code': 3" in msg) or ('"code": 3' in msg)
                if bad_creds:
                    try:
                        keyring.delete_password(self.keyring_service, self.user)
                    except keyring.errors.PasswordDeleteError:
                        pass
                    print("❌ Invalid credentials. Please re-enter password.")
                    pw = None
                    first_attempt = False
                    continue
                raise  # other errors bubble up

# ====================== CLASS 2: INVENTORY ======================
class Inventory:
    """Loads the XO object graph, filters templates, and resolves VIFs for a template."""
    def __init__(self, client: XOClient):
        self.client = client
        self.objects: Dict[str, Dict[str, Any]] = {}

    async def refresh(self):
        self.objects = await self.client.call("xo.getAllObjects")

    def list_templates(self, prefix: str = "") -> List[Dict[str, Any]]:
        temps = [o for o in self.objects.values() if o.get("type") == "VM-template"]
        if prefix:
            pre = prefix.lower()
            temps = [t for t in temps if (t.get("name_label") or "").lower().startswith(pre)]
        return sorted(temps, key=lambda t: (t.get("name_label") or "").lower())

    def list_vms(self) -> List[Dict[str, Any]]:
        vms = [o for o in self.objects.values() if o.get("type") == "VM" and not o.get("is_a_template") and not o.get("is_a_snapshot")]
        return sorted(vms, key=lambda v: (v.get("name_label") or "").lower())

    def get_network_label(self, net_id: Optional[str]) -> str:
        if not net_id:
            return ""
        net = self.objects.get(net_id)
        return net.get("name_label") if net else (net_id or "")

    def resolve_template_vifs(self, template_obj: Dict[str, Any]) -> List[Dict[str, str]]:
        """
        Return VIFs for vm.create: [{'network': '<id: str>', 'mac': '<mac?>'}, ...]
        Handle both 'network' and '$network' ref styles.
        """
        res: List[Dict[str, str]] = []
        for vif_id in template_obj.get("VIFs", []):
            vif = self.objects.get(vif_id)
            if vif and vif.get("type") == "VIF":
                net_id = vif.get("network") or vif.get("$network")
                entry: Dict[str, str] = {"network": str(net_id) if net_id else ""}
                mac = vif.get("MAC")
                if mac:
                    entry["mac"] = mac
                res.append(entry)
        return res

# ====================== CLASS 3: CONFIG REPO ======================
class ConfigRepo:
    """Fetches saved configs (cloudConfig.getAll) and classifies user vs network."""
    def __init__(self, client: XOClient):
        self.client = client

    @staticmethod
    def _classify_text(text: str) -> str:
        lower = (text or "").strip().lower()
        if lower.startswith("#cloud-config"):
            return "cloud-config"
        if lower.startswith("#network") or lower.startswith("#network-config"):
            return "network-config"
        if "network:" in lower and ("version: 2" in lower or any(k in lower for k in ("ethernets:", "vlans:", "bonds:", "bridges:", "wifis:"))):
            return "network-config"
        if "version:" in lower and "config:" in lower and any(k in lower for k in ("type: dhcp", "type: static", "routes:", "nameserver", "address:", "subnets:")):
            return "network-config"
        return "cloud-config"

    async def fetch(self) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """Returns (user_configs, network_configs). Each item has id,name,template,type."""
        configs = await self.client.call("cloudConfig.getAll", {})
        user, net = [], []
        for c in configs:
            name = c.get("name", c.get("name_label", "unnamed"))
            template_text = c.get("template", "") or ""
            ctype = self._classify_text(template_text)
            item = {"id": c.get("id"), "name": name, "template": template_text, "type": ctype}
            (user if ctype == "cloud-config" else net).append(item)
        user.sort(key=lambda x: x["name"].lower())
        net.sort(key=lambda x: x["name"].lower())
        return user, net

    @staticmethod
    def find_by_id(configs: List[Dict[str, Any]], config_id: str) -> Optional[Dict[str, Any]]:
        for c in configs:
            if c.get("id") == config_id:
                return c
        return None